"""
阶段17：成片合成 (Final Cut)
最后一步，根据剧本先粗剪，再精剪，生成成片
"""

import concurrent.futures
import json
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from config.prompts import ROUGH_CUT_REFINEMENT_PROMPT
from config.schemas import RoughCutRefinementResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client
from utils.retry_utils import retry_on_exception
from utils.shared_state import shutdown_event
from utils.tts_utils import get_tts_client
from utils.video_utils import video_processor


class Stage17FinalCut(BaseStage):
    """阶段17：成片合成 (Final Cut)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.editing_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(f"已为剪辑决策阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}")
        else:
            self.editing_client = ai_client

    @property
    def stage_number(self) -> int:
        return 17

    @property
    def stage_name(self) -> str:
        return "成片合成 (Final Cut)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 【核心修改】现在依赖于 Stage 16
        stage16_status = self.db_manager.get_stage_status(self.video_id, 16)
        if not stage16_status or stage16_status["status"] != "completed":
            return False, "阶段16 (剧本创作) 尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行自动化生产与合成"""
        try:
            temp_dir = settings.OUTPUT_DIR / f"temp_production_{self.video_id}"
            if force_level == "full":
                self.logger.info("强制模式 'full': 清理所有已生成的TTS音频和XML文件。")
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
                # 旧规则文件
                for f in settings.OUTPUT_DIR.glob(f"final_cut_{self.video_id}_*.xml"):
                    f.unlink()

                # 新规则文件：<video_hash>-17-final_cut.xml
                video_info = self.get_video_info()
                if video_info and video_info.get("file_hash"):
                    xml_path = settings.OUTPUT_DIR / f"{video_info['file_hash']}-{self.stage_number}-final_cut.xml"
                    if xml_path.exists():
                        xml_path.unlink()

            master_script = self.db_manager.get_master_script(self.video_id)
            if not master_script:
                self.logger.error("未找到主旁白文案，请先运行阶段16。")
                return False

            return self._produce_from_script_by_beats(master_script, **kwargs)

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _produce_from_script_by_beats(self, master_script: List[Dict[str, Any]], **kwargs) -> bool:
        """按音画节拍（Beats）处理剧本，构建时间线。"""
        # 1. 加载上下文
        story_outline_data = self.db_manager.get_stage_output(self.video_id, 13, "story_outline")
        if not story_outline_data or not story_outline_data.get("story_outline"):
            self.logger.error("无法加载故事大纲，无法执行精剪流程。")
            return False
        scenes_outline = {s["scene_number"]: s for s in story_outline_data["story_outline"]}

        # 2. 初始化
        temp_dir = settings.OUTPUT_DIR / f"temp_production_{self.video_id}"
        temp_dir.mkdir(exist_ok=True, parents=True)
        tts_voice = kwargs.get("tts_voice", settings.TTS_VOICE)

        # 3. 并行处理所有音画节拍
        total_beats = len(master_script)
        completed_count, failed_count = 0, 0
        beat_results = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
            future_to_beat_idx = {
                executor.submit(self._process_single_beat, beat, i, tts_voice, temp_dir, scenes_outline): i
                for i, beat in enumerate(master_script) # 直接遍历 master_script (ScriptBeat 列表)
            }

            for future in concurrent.futures.as_completed(future_to_beat_idx):
                beat_idx = future_to_beat_idx[future]
                try:
                    if shutdown_event.is_set():
                        for f in future_to_beat_idx:
                            f.cancel()
                        break
                    result = future.result()
                    if result:
                        beat_results.append(result)
                        completed_count += 1
                    else:
                        failed_count += 1
                    progress_info = f"处理中 - 已完成 {completed_count + failed_count}/{total_beats} (成功: {completed_count}, 失败: {failed_count})"
                    self.update_progress(progress_info)
                except Exception as exc:
                    self.logger.error(f"处理音画节拍 {beat_idx + 1} 时产生严重异常: {exc}", exc_info=True)
                    failed_count += 1

        if failed_count > 0 or shutdown_event.is_set():
            self.logger.error("一个或多个音画节拍未能成功处理，阶段失败。")
            return False

        # 4. 按原始节拍顺序组装时间线
        beat_results.sort(key=lambda x: x["beat_idx"]) # 确保按原始顺序排序
        video_events, narration_audio_events = self._assemble_timeline_from_beats(beat_results)

        # 5. 检查并生成XML
        if not video_events:
            self.logger.error("未能构建任何有效的时间线事件。")
            return False
        return self._compose_final_video(video_events, narration_audio_events)

    @retry_on_exception()
    def _process_single_beat(
        self, beat: Dict[str, Any], beat_idx: int, tts_voice: str, temp_dir: Path, scenes_outline: Dict[int, Any]
    ) -> Optional[Dict[str, Any]]:
        """处理单个音画节拍的核心逻辑。"""
        audio_content = beat.get("audio_content")
        narration_type = beat.get("narration_type")

        audio_path, audio_duration = None, 0.0
        use_original_audio = False # 默认不使用原声

        if audio_content:
            if narration_type in ["NARRATOR", "INNER_MONOLOGUE"]:
                tts_client = get_tts_client()
                audio_path = temp_dir / f"tts_beat_{beat_idx + 1}_{hash(audio_content)}.mp3"
                if tts_client.generate_audio(audio_content, audio_path, voice_name=tts_voice):
                    audio_duration = video_processor.get_media_info(audio_path).get("duration", 0)
                else:
                    audio_path = None  # 生成失败
            elif narration_type == "CHARACTER_DIALOGUE":
                # 对于角色对白，我们不生成TTS，而是尝试匹配原视频中的对话片段
                # 此时 audio_path 和 audio_duration 保持 None/0，通过 use_original_audio 标记
                use_original_audio = True
                self.logger.debug(f"节拍 {beat_idx + 1} 为角色对白，将尝试匹配原视频音频。")
            else:
                self.logger.warning(f"节拍 {beat_idx + 1} 的 narration_type '{narration_type}' 未知，将跳过音频处理。")
        # --- 新增：旁白节拍同样保留原声 ---
        # 只要本节拍包含旁白/对白 (audio_content 非空)，无论 narration_type，
        # 都启用 use_original_audio，后续 XML 将利用 ducking_info 自动降低原音音量
        if audio_content:
            use_original_audio = True
        else:
            self.logger.debug(f"节拍 {beat_idx + 1} 无音频内容，为纯视觉节拍。")

        scene_num = beat.get("source_scene_number")
        if scene_num is None or scene_num not in scenes_outline:
            self.logger.warning(f"音画节拍 {beat_idx + 1} 缺少有效的源场景编号，无法匹配视频。")
            return {
                "beat_idx": beat_idx,
                "audio_path": audio_path,
                "audio_duration": audio_duration,
                "final_shots": [],
                "use_original_audio": use_original_audio,
            }

        scene_outline = scenes_outline[scene_num]
        candidate_shots = self._get_candidate_shots_for_scene(scene_outline)

        # 对于纯视觉节拍或角色对白节拍，script_text 传递 visual_description
        # 对于旁白节拍，script_text 传递 audio_content
        text_for_refinement = audio_content if audio_content else beat.get("visual_description", "")

        final_shot_ids = self._refine_rough_cut(text_for_refinement, audio_duration, candidate_shots)
        final_shots = [shot for shot in candidate_shots if shot["shot_order_id"] in final_shot_ids]
        if not final_shots:
            self.logger.warning(f"音画节拍 {beat_idx + 1} 未能匹配到任何镜头。")
            return {
                "beat_idx": beat_idx,
                "audio_path": audio_path,
                "audio_duration": audio_duration,
                "final_shots": [],
                "use_original_audio": use_original_audio,
            }
        final_shots.sort(key=lambda x: final_shot_ids.index(x["shot_order_id"]))

        return {
            "beat_idx": beat_idx,
            "audio_path": audio_path,
            "audio_duration": audio_duration,
            "final_shots": final_shots,
            "use_original_audio": use_original_audio,
        }

    def _assemble_timeline_from_beats(
        self, beat_results: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """根据处理过的音画节拍结果，按顺序组装时间线。"""
        video_events, narration_audio_events = [], []
        timeline_pos, current_video_track_index = 0.0, 0 # 视频轨道索引

        for result in beat_results:
            audio_path, audio_duration, final_shots, use_original_audio = (
                result["audio_path"],
                result["audio_duration"],
                result["final_shots"],
                result["use_original_audio"],
            )

            visual_duration = sum(s.get("duration_sec", s["end_time"] - s["start_time"]) for s in final_shots)
            segment_duration = max(audio_duration, visual_duration) # 确保整个节拍时长足够长

            # 处理旁白/TTS音频
            if audio_path:
                narration_audio_events.append({
                    "path": audio_path,
                    "timeline_start": timeline_pos,
                    "timeline_end": timeline_pos + audio_duration,
                    "timeline_duration": audio_duration,
                    "source_in": 0,
                    "source_out": audio_duration,
                })

            # 处理视频片段和其原声
            current_video_pos_in_segment = 0.0
            for shot in final_shots:
                shot_duration = shot.get("duration_sec", shot["end_time"] - shot["start_time"])

                # 视频事件
                event = {
                    "path": Path(shot["clip_url"]),
                    "timeline_start": timeline_pos + current_video_pos_in_segment,
                    "timeline_end": timeline_pos + current_video_pos_in_segment + shot_duration,
                    "timeline_duration": shot_duration,
                    "source_in": 0,
                    "source_out": shot_duration,
                    "track_index": current_video_track_index, # 使用视频轨道索引
                    "ducking_info": {"start": timeline_pos, "end": timeline_pos + audio_duration} if audio_path else None,
                    "use_original_audio": use_original_audio # 传递是否使用原声的标记
                }
                video_events.append(event)
                current_video_pos_in_segment += shot_duration

            timeline_pos += segment_duration
            # 切换视频轨道，实现交错剪辑效果
            current_video_track_index = 1 - current_video_track_index

        return video_events, narration_audio_events

    def _get_candidate_shots_for_scene(self, scene_outline: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取一个场景的所有候选镜头详情。"""
        candidate_ids_str = scene_outline.get("candidate_clip_ids", [])
        candidate_order_ids = []
        scene_numbers_to_resolve = []
        for cid_str in candidate_ids_str:
            if isinstance(cid_str, str) and cid_str.startswith("scene_"):
                try:
                    scene_numbers_to_resolve.append(int(cid_str.split("_")[1]))
                except (ValueError, IndexError):
                    pass
            else:
                try:
                    candidate_order_ids.append(int(cid_str))
                except (ValueError, TypeError):
                    pass

        if scene_numbers_to_resolve:
            resolved_ids = set(candidate_order_ids)
            for s_num in scene_numbers_to_resolve:
                shots = self.db_manager.get_shots_by_scene_number(self.video_id, s_num)
                for shot in shots:
                    if shot.get("shot_order_id") is not None:
                        resolved_ids.add(shot["shot_order_id"])
                # 过滤掉不在当前调试场景限制内的场景
                if settings.DEBUG_MODE_SCENE_LIMIT > 0 and s_num > settings.DEBUG_MODE_SCENE_LIMIT:
                    continue
            candidate_order_ids = sorted(list(resolved_ids))

        shots = self.db_manager.get_shots_by_order_ids(self.video_id, candidate_order_ids)
        for shot in shots:
            shot["duration_sec"] = shot["end_time"] - shot["start_time"]
        return shots

    def _refine_rough_cut(
        self, script_text: str, narration_duration: float, candidate_shots: List[Dict[str, Any]]
    ) -> List[int]:
        """调用AI进行精剪决策。"""
        if not candidate_shots:
            return []

        # 如果没有旁白（即这是一个原声片段），则默认使用所有候选镜头
        if not script_text:
            return [shot["shot_order_id"] for shot in candidate_shots]

        cleaned_shots = [
            {
                "shot_order_id": shot["shot_order_id"],
                "duration_sec": shot["duration_sec"],
                "visual_description": shot.get("visual_description", ""),
                "dialogue": shot.get("dialogue", ""),
            }
            for shot in candidate_shots
        ]

        prompt = ROUGH_CUT_REFINEMENT_PROMPT.format(
            scene_script_text=script_text,
            narration_duration=narration_duration,
            candidate_shots_json=json.dumps(cleaned_shots, ensure_ascii=False, indent=2),
            total_candidate_duration=sum(s["duration_sec"] for s in cleaned_shots),
        )

        result = self.editing_client.call_ai_with_tool(prompt, response_model=RoughCutRefinementResponse)
        self.logger.info(f"AI精剪决策完成。理由: {result.justification}")
        return result.selected_shot_order_ids

    def _compose_final_video(
        self, video_events: List[Dict[str, Any]], narration_audio_events: List[Dict[str, Any]]
    ) -> bool:
        """根据时间线事件生成最终的FCP7 XML。"""
        video_info = self.get_video_info()
        video_hash = video_info.get("file_hash") if video_info else None
        if video_hash:
            xml_output_path = settings.OUTPUT_DIR / f"{video_hash}-{self.stage_number}-final_cut.xml"
        else:
            self.logger.warning("无法获取视频哈希值，使用后备命名方案。")
            xml_output_path = settings.OUTPUT_DIR / f"video_{self.video_id}-{self.stage_number}-final_cut.xml"
        project_title = (
            video_info.get("video_name", f"AutoCutter Project {self.video_id}")
            if video_info
            else f"AutoCutter Project {self.video_id}"
        )

        success = video_processor.generate_fcp7_xml_from_timeline(
            video_events=video_events,
            audio_events=narration_audio_events,
            output_path=xml_output_path,
            title=project_title,
        )

        if success:
            self.logger.info(f"剪辑完成！项目文件已生成: {xml_output_path}")
            return True
        else:
            self.logger.error("生成XML文件失败，请检查 utils/video_utils.py 中的错误日志。")
            return False
