"""
阶段11：D2S 读者模块 (Reader)
第二步：因果链接推断
"""

import json
from typing import Optional

from config.schemas import CausalLinkInferenceResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client


class Stage11ReaderCausalLink(BaseStage):
    """阶段11：D2S 读者模块 - 因果链接推断"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        # 此阶段的核心是AI解析，可以使用高级AI客户端
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.reader_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(
                f"已为D2S Reader(因果推断)阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}"
            )
        else:
            self.reader_client = ai_client

    @property
    def stage_number(self) -> int:
        return 11

    @property
    def stage_name(self) -> str:
        return "D2S Reader (因果推断)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage10_status = self.db_manager.get_stage_status(self.video_id, 10)
        if not stage10_status or stage10_status["status"] != "completed":
            return False, "阶段10 (D2S Reader 事件识别) 尚未完成"

        events_data = self.db_manager.get_stage_output(self.video_id, 10, "narrative_events")
        if not events_data:
            return False, "数据库中未找到叙事事件列表数据"

        if not self.db_manager.get_all_research_summaries(self.video_id):
            return False, "数据库中未找到研究资料摘要"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行因果链接推断并构建因果图谱"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的因果图谱数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "causal_graph")

        # 检查数据库中是否已有输出
        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "causal_graph")
        if existing_output:
            self.logger.info("从数据库加载已缓存的因果图谱数据。")
            return True

        # 1. 从数据库加载所需数据
        try:
            master_context = self.db_manager.get_stage_output(self.video_id, 9, "creative_brief")
            events_data = self.db_manager.get_stage_output(self.video_id, 10, "narrative_events")
            narrative_events = events_data.get("narrative_events", []) if events_data else []
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)

            if not master_context or not narrative_events:
                self.logger.error("无法从数据库加载前置阶段的输出数据或叙事事件列表为空。")
                return False

        except Exception as e:
            self.logger.error(f"无法加载前置数据: {e}")
            return False

        # 2. 调用AI进行因果推断
        self.update_progress("AI正在推断事件间的因果关系...")
        try:
            from config.prompts import CAUSAL_LINK_INFERENCE_PROMPT

            prompt = CAUSAL_LINK_INFERENCE_PROMPT.format(
                research_summary=research_summary,
                project_info_json=json.dumps(master_context.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters"), ensure_ascii=False, indent=2),
                narrative_events_json=json.dumps(narrative_events, ensure_ascii=False, indent=2),
                language=settings.NARRATION_LANGUAGE,
            )

            result = self.reader_client.call_ai_with_tool(prompt, response_model=CausalLinkInferenceResponse)

            if not result:
                self.logger.error("AI未能返回任何因果链接推断结果。")
                return False

            # 3. 组装并保存因果图谱到数据库
            causal_graph = {"nodes": narrative_events, "edges": result.model_dump().get("causal_links", [])}
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="causal_graph",
                output_data=causal_graph
            )

            self.logger.info(f"✅ 成功推断出 {len(causal_graph['edges'])} 条因果链接并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
