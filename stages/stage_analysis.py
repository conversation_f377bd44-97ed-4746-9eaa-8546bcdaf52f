"""
阶段1：镜头分析 (Shot Analysis)
包含镜头检测、分割、AI分析和向量化
"""

import concurrent.futures
import csv
import math  # 新增此行
import queue
import subprocess
import threading
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient
from utils.shared_state import shutdown_event
from utils.storage_utils import get_tos_client
from utils.video_utils import video_processor


class Stage1Analysis(BaseStage):
    """阶段1：镜头分析 (Shot Analysis)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self._db_queue = queue.Queue()
        self._stop_db_writer = threading.Event()
        # 为视觉分析和向量化初始化一个专用的AI客户端
        advanced_ai_config = settings.get_advanced_ai_config()
        self.vision_client = AIClient(
            api_keys=advanced_ai_config["api_keys"],
            base_url=advanced_ai_config["base_url"],
            model=advanced_ai_config["model"],
            embedding_model=advanced_ai_config["embedding_model"],
            timeout=advanced_ai_config["timeout"],
        )
        self.logger.debug("已为镜头分析阶段初始化专用的视觉AI客户端。")

    def _db_writer_worker(self):
        """从队列中获取并执行数据库写入任务"""
        self.logger.info("数据库写入线程已启动。")
        while not self._stop_db_writer.is_set() or not self._db_queue.empty():
            try:
                db_task, args = self._db_queue.get(timeout=1)
                try:
                    db_task(*args)
                except Exception as e:
                    self.logger.error(f"执行数据库任务时出错: {e}")
                finally:
                    self._db_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"数据库写入线程的队列操作出错: {e}")
        self.logger.info("数据库写入线程已停止。")

    def _calculate_adaptive_batch_size(self, total_items: int, target_batch_size: int) -> int:
        """
        计算自适应批量大小，以避免出现数量过少的最后一个批次。
        """
        if total_items == 0 or target_batch_size <= 0:
            return 1  # 返回一个安全的默认值

        # 如果总数小于或等于目标批量大小，则一次性处理
        if total_items <= target_batch_size:
            return total_items

        remainder = total_items % target_batch_size

        # 如果余数不为0，且小于目标批量的一半，则触发自适应调整
        if 0 < remainder < (target_batch_size / 2):
            # 向上取整计算出需要的批次数
            num_batches = math.ceil(total_items / target_batch_size)
            # 向下取整计算出更均匀的实际批量大小
            actual_batch_size = math.floor(total_items / num_batches)

            self.logger.info(
                f"自适应批量大小调整: "
                f"目标批量 {target_batch_size} 会产生一个大小为 {remainder} 的小批次。 "
                f"将使用 {num_batches} 个批次，实际批量大小调整为 {actual_batch_size}。"
            )
            return actual_batch_size
        else:
            # 如果余数足够大或为0，则使用原始目标批量大小
            self.logger.info(f"使用目标批量大小: {target_batch_size}。")
            return target_batch_size

    def _re_vectorize_shots(self) -> bool:
        """
        强制重新向量化模式的执行逻辑。
        此方法从数据库读取所有已分析的镜头，批量重新生成文本嵌入，并更新数据库记录。
        """
        from database.models import Shots

        self.update_progress("开始为所有现有镜头重新生成文本向量...")
        with self.db_manager.get_session() as session:
            shots_to_process = session.query(Shots).filter(Shots.video_id == self.video_id).all()
            if not shots_to_process:
                self.logger.info("未找到任何镜头，无需进行向量化。")
                return True

            # 1. 收集所有需要处理的文本和对应的镜头ID
            texts_to_vectorize = []
            shot_ids_for_update = []
            total_shots = len(shots_to_process)
            self.update_progress(f"准备 {total_shots} 个镜头的描述文本...")

            for shot in shots_to_process:
                descriptive_text = " ".join(
                    filter(
                        None,
                        [
                            shot.visual_description,
                            shot.action,
                            shot.people,
                            shot.setting,
                            shot.emotion,
                            shot.key_objects,
                            shot.dialogue, # 新增此行，包含对话内容
                        ],
                    )
                ).strip()
                if descriptive_text:
                    texts_to_vectorize.append(descriptive_text)
                    shot_ids_for_update.append(shot.id)

            if not texts_to_vectorize:
                self.logger.info("没有找到任何包含有效描述文本的镜头可供向量化。")
                return True

            # 2. 按批次循环调用嵌入模型
            total_texts = len(texts_to_vectorize)
            target_batch_size = settings.EMBEDDING_BATCH_SIZE
            batch_size = self._calculate_adaptive_batch_size(total_texts, target_batch_size)

            all_embedding_vectors = []
            num_batches = (total_texts + batch_size - 1) // batch_size

            for i in range(0, total_texts, batch_size):
                batch_texts = texts_to_vectorize[i : i + batch_size]
                batch_num = (i // batch_size) + 1
                self.update_progress(f"正在为 {total_texts} 条文本进行批量向量化 (批次 {batch_num}/{num_batches})...")

                embedding_vectors_batch = self.vision_client.get_embedding(batch_texts)

                if not embedding_vectors_batch or len(embedding_vectors_batch) != len(batch_texts):
                    self.logger.error(f"批量向量化失败 (批次 {batch_num}) 或返回的向量数量与输入不匹配。")
                    return False

                all_embedding_vectors.extend(embedding_vectors_batch)

            self.logger.info(f"批量向量化成功，共获得了 {len(all_embedding_vectors)} 个向量。")

            # 3. 准备批量更新
            updates = [
                {"id": shot_id, "text_embedding_vector": vector.tobytes()}
                for shot_id, vector in zip(shot_ids_for_update, all_embedding_vectors)
            ]

            # 4. 执行批量数据库更新
            if updates:
                self.update_progress(f"正在将 {len(updates)} 个更新后的向量写入数据库...")
                session.bulk_update_mappings(Shots.__mapper__, updates)
                self.logger.info(f"成功为 {len(updates)} 个镜头更新了文本向量。")
            else:
                self.logger.info("没有生成任何新的向量可供更新。")

        return True

    @property
    def stage_number(self) -> int:
        return 1

    @property
    def stage_name(self) -> str:
        return "镜头分析 (Shot Analysis)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行镜头分析"""
        self.force_level = force_level  # 设置实例属性以供其他方法使用

        # --- 新增：处理 vectorize 模式 ---
        if force_level == "vectorize":
            self.logger.info("--- 强制向量化模式 ---")
            return self._re_vectorize_shots()
        # --- 新增结束 ---

        try:
            skip_until = kwargs.get("skip_until", 0.0)
            genre = kwargs.get("genre", "personal")

            # 新增逻辑：如果强制执行，但镜头分析已完成，则只重新提取人脸
            run_shot_analysis = True
            if force_level:
                from database.models import Shots

                with self.db_manager.get_session() as session:
                    # 检查是否存在已完成的镜头分析记录
                    has_completed_shots = (
                        session.query(Shots.id)
                        .filter(Shots.video_id == self.video_id, Shots.status == "completed")
                        .first()
                        is not None
                    )

                if has_completed_shots:
                    self.logger.info(
                        f"强制模式 '{force_level}': 检测到已存在的镜头分析结果。将跳过镜头分析，仅重新提取人脸。"
                    )
                    run_shot_analysis = False

            if run_shot_analysis:
                self.update_progress("开始并行分析镜头内容")
                if not self._perform_shot_analysis(force_level=force_level, skip_until=skip_until, genre=genre):
                    return False

            if shutdown_event.is_set():
                return False

            if not self._perform_face_extraction_sequentially(skip_until=skip_until):
                return False

            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _process_single_shot(self, shot_info: Dict[str, Any]) -> bool:
        """处理单个已提取镜头的流程：生成低质版本、上传、视觉分析、向量化、保存"""
        start_time, end_time, clip_path, genre = (
            shot_info["start"],
            shot_info["end"],
            shot_info["path"],
            shot_info["genre"],
        )

        try:
            low_quality_video_path = clip_path.with_suffix(".lowres.mp4")
            if not video_processor.create_low_quality_video(
                clip_path, low_quality_video_path, settings.LOW_QUALITY_BITRATE
            ):
                raise RuntimeError("创建低质量视频失败")

            low_quality_object_key = f"clips/low-res/{low_quality_video_path.name}"
            tos_client = get_tos_client()
            low_quality_clip_url = tos_client.upload_file(low_quality_video_path, low_quality_object_key)
            if not low_quality_clip_url:
                raise RuntimeError(f"上传低画质片段 {low_quality_video_path.name} 失败")

            visual_analysis_result = self.vision_client.analyze_scene(
                low_quality_clip_url, end_time - start_time, genre=genre
            )
            self.logger.info(f"成功分析镜头 {start_time:.2f}s - {end_time:.2f}s")

            # --- 文本向量化 ---
            descriptive_text = " ".join(
                filter(
                    None,
                    [
                        visual_analysis_result.get("visual_description"),
                        visual_analysis_result.get("main_action"),
                        visual_analysis_result.get("people"),
                        visual_analysis_result.get("setting"),
                        visual_analysis_result.get("emotion"),
                        visual_analysis_result.get("key_objects"),
                        visual_analysis_result.get("dialogue"), # 新增此行，确保对话也参与向量化
                    ],
                )
            )

            text_embedding_vector = None
            if descriptive_text:
                self.logger.debug(f"正在为镜头 {start_time:.2f}s 生成文本嵌入...")
                text_embedding_vector = self.vision_client.get_embedding(descriptive_text)

            clip_file_size = clip_path.stat().st_size

            self._save_shot_analysis(
                start_time,
                end_time,
                str(clip_path.resolve()),
                clip_file_size,
                low_quality_clip_url,
                visual_analysis_result,
                text_embedding_vector,
            )
            return True
        except Exception as e:
            self.logger.error(f"处理镜头 {start_time:.2f}s-{end_time:.2f}s 时出错: {e}")
            self._save_shot_error(start_time, end_time, str(e))
            return False

    def _run_scenedetect_cli(
        self, input_video_path: Path, output_dir: Path, file_hash: str, video_height: int, skip_until: float = 0.0
    ) -> Optional[Path]:
        """
        使用 scenedetect 命令行工具进行镜头检测和视频分割。
        返回生成的镜头列表CSV文件的路径。
        """
        self.logger.info(f"开始使用 scenedetect CLI 对 '{input_video_path.name}' 进行处理...")
        output_dir.mkdir(parents=True, exist_ok=True)

        cmd = ["scenedetect"]
        if video_height > 540:
            downscale_factor = max(2, round(video_height / 480))
            self.logger.info(f"视频高度为 {video_height}p，动态计算下采样因子为: {downscale_factor}")
            cmd.extend(["-d", str(downscale_factor)])

        filename_template = f"{file_hash}-shot-$SCENE_NUMBER.mp4"
        cmd.extend(["-i", str(input_video_path)])

        if skip_until > 0:
            self.logger.info(f"将跳过视频开头 {skip_until} 秒进行镜头检测。")
            cmd.extend(["time", "--start", str(skip_until)])

        cmd.extend(["split-video", "-o", str(output_dir), "-p", settings.SCENEDETECT_PRESET, "-f", filename_template])

        detector = settings.SCENE_DETECTOR
        if detector == "adaptive":
            cmd.extend(["detect-adaptive"])
        elif detector == "content":
            cmd.extend(["detect-content"])
        elif detector == "threshold":
            cmd.extend(["detect-threshold", "-t", str(settings.SCENE_DETECTION_THRESHOLD)])
        else:
            self.logger.warning(f"无效的镜头检测器类型: '{detector}'。将回退到默认的 'adaptive' 模式。")
            cmd.extend(["detect-adaptive"])

        csv_filename = f"{file_hash}-shots.csv"
        csv_full_path = output_dir / csv_filename
        cmd.extend(["list-scenes", "-f", str(csv_full_path)])

        try:
            self.logger.info(f"正在执行 scenedetect 命令: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding="utf-8", errors="ignore"
            )
            if process.stdout:
                for line in iter(process.stdout.readline, ""):
                    print(line.strip())
            return_code = process.wait()

            if return_code == 0:
                self.logger.info("scenedetect CLI 执行成功。")
                if csv_full_path.exists():
                    return csv_full_path
                else:
                    self.logger.error(f"scenedetect 执行完毕，但未在预期路径找到镜头CSV文件: {csv_full_path}")
                    return None
            else:
                self.logger.error(f"scenedetect CLI 执行失败。返回码: {return_code}")
                return None
        except FileNotFoundError:
            self.logger.error("`scenedetect` 命令未找到。请确保 PySceneDetect 已通过 pip 安装。")
            return None
        except Exception as e:
            self.logger.error(f"执行 scenedetect CLI 时发生未知错误: {e}", exc_info=True)
            return None

    def _parse_scenedetect_csv(self, csv_path: Path) -> List[Tuple[float, float, int, int]]:
        """解析 scenedetect 生成的镜头列表CSV文件。"""
        shots = []
        try:
            with open(csv_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            csv_start_index = next((i for i, line in enumerate(lines) if line.strip().startswith("Scene Number,")), -1)
            if csv_start_index == -1:
                self.logger.error(f"在 {csv_path.name} 中未找到CSV表头 'Scene Number,'。")
                return []
            reader = csv.DictReader(lines[csv_start_index:])
            for row in reader:
                start_sec = float(row["Start Time (seconds)"])
                end_sec = float(row["End Time (seconds)"])
                start_frame = int(row["Start Frame"])
                end_frame = int(row["End Frame"])
                shots.append((start_sec, end_sec, start_frame, end_frame))
            self.logger.info(f"从 {csv_path.name} 中成功解析出 {len(shots)} 个镜头。")
            return shots
        except Exception as e:
            self.logger.error(f"解析镜头CSV文件 {csv_path} 时出错: {e}", exc_info=True)
            return []

    def _get_shot_status(self, start_time: float, end_time: float) -> Optional[str]:
        """获取单个镜头的状态"""
        from database.models import Shots

        with self.db_manager.get_session() as session:
            return (
                session.query(Shots.status)
                .filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time)
                .scalar()
            )

    def _delete_shot_record(self, start_time: float, end_time: float):
        """删除指定的镜头记录"""
        from database.models import Shots

        with self.db_manager.get_session() as session:
            session.query(Shots).filter_by(video_id=self.video_id, start_time=start_time, end_time=end_time).delete()

    def _save_shot_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        visual_analysis: Dict[str, Any],
        text_embedding_vector: Optional[np.ndarray],
    ):
        """将镜头分析结果放入数据库写入队列"""
        args = (
            start_time,
            end_time,
            clip_local_path,
            clip_file_size,
            low_quality_clip_url,
            visual_analysis,
            text_embedding_vector,
        )
        self._db_queue.put((self._execute_save_shot_analysis, args))

    def _execute_save_shot_analysis(
        self,
        start_time: float,
        end_time: float,
        clip_local_path: str,
        clip_file_size: int,
        low_quality_clip_url: str,
        visual_analysis: Dict[str, Any],
        text_embedding_vector: Optional[np.ndarray],
    ):
        """实际执行保存镜头分析结果的数据库操作"""
        from database.models import Shots

        shot_analysis = visual_analysis.get("shot_analysis") or {}
        new_shot = Shots(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_local_path,
            low_quality_clip_url=low_quality_clip_url,
            clip_file_size=clip_file_size,
            visual_description=visual_analysis.get("visual_description", ""),
            people=visual_analysis.get("people", ""),
            setting=visual_analysis.get("setting", ""),
            action=visual_analysis.get("main_action", ""),
            emotion=visual_analysis.get("emotion", ""),
            key_objects=visual_analysis.get("key_objects", ""),
            shot_type=shot_analysis.get("shot_type", ""),
            camera_angle=shot_analysis.get("camera_angle", ""),
            camera_movement=shot_analysis.get("camera_movement", ""),
            composition=shot_analysis.get("composition", ""),
            lighting=visual_analysis.get("lighting", ""),
            action_intensity=visual_analysis.get("action_intensity"),
            subtext=visual_analysis.get("subtext"),
            comedy_type=visual_analysis.get("comedy_type"),
            on_screen_text=visual_analysis.get("on_screen_text", ""),
            text_embedding_vector=text_embedding_vector.tobytes() if text_embedding_vector is not None else None,
            status="completed",
        )
        with self.db_manager.get_session() as session:
            session.add(new_shot)

    def _save_shot_error(self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None):
        """将镜头分析错误放入数据库写入队列"""
        self._db_queue.put((self._execute_save_shot_error, (start_time, end_time, error_message, clip_url)))

    def _execute_save_shot_error(
        self, start_time: float, end_time: float, error_message: str, clip_url: Optional[str] = None
    ):
        """实际执行保存镜头分析错误的数据库操作"""
        from database.models import Shots

        new_shot_error = Shots(
            video_id=self.video_id,
            start_time=start_time,
            end_time=end_time,
            clip_url=clip_url,
            status="failed",
            error_message=error_message,
        )
        with self.db_manager.get_session() as session:
            session.add(new_shot_error)

    def _extract_face_embeddings(self, clip_path: Path) -> List[Dict[str, Any]]:
        """从视频片段中提取所有人脸的嵌入向量"""
        from utils.face_utils import get_face_embedder

        try:
            face_embedder = get_face_embedder()
            return face_embedder.extract_embeddings_from_clip(clip_path)
        except Exception as e:
            self.logger.error(f"调用 face_embedder 时发生未知异常: {e}", exc_info=True)
            return []

    def _perform_face_extraction_sequentially(self, skip_until: float = 0.0) -> bool:
        """串行执行人脸提取，并更新数据库记录。"""
        from database.models import FaceEmbeddings, Shots

        # 如果是强制模式，无论soft还是full，都应该清理旧的人脸数据。
        # 这对于跳过镜头分析、仅重做人脸提取的场景至关重要。
        if self.force_level:
            self.logger.info(f"强制模式 '{self.force_level}': 正在清理旧的人脸嵌入数据以便重新提取...")
            with self.db_manager.get_session() as session:
                subquery = session.query(Shots.id).filter(Shots.video_id == self.video_id).scalar_subquery()
                # 仅当存在关联的镜头时才执行删除，以避免空的IN子句问题
                if session.query(subquery.exists()).scalar():
                    deleted_count = (
                        session.query(FaceEmbeddings)
                        .filter(FaceEmbeddings.shot_id.in_(subquery))
                        .delete(synchronize_session=False)
                    )
                    self.logger.info(f"旧的人脸嵌入数据已清理 (删除了 {deleted_count} 条记录)。")
                else:
                    self.logger.info("未找到与此视频关联的镜头，无需清理人脸数据。")

        self.update_progress("开始串行提取人脸特征")

        with self.db_manager.get_session() as session:
            query = (
                session.query(Shots.id, Shots.clip_url, Shots.start_time)
                .filter(Shots.video_id == self.video_id, Shots.status == "completed", ~Shots.faces.any())
                .order_by(Shots.start_time)
            )
            if skip_until > 0:
                self.logger.info(f"人脸提取将跳过 {skip_until} 秒前的所有镜头。")
                query = query.filter(Shots.start_time >= skip_until)
            shots_to_process = query.all()

        if not shots_to_process:
            self.logger.info("没有需要进行人脸提取的新镜头。")
            return True

        total_shots = len(shots_to_process)
        self.logger.info(f"发现 {total_shots} 个需要提取人脸特征的新镜头，开始串行处理...")

        for i, (shot_id, clip_url, _) in enumerate(shots_to_process):
            if shutdown_event.is_set():
                self.logger.warning("检测到关闭信号，中断人脸提取。")
                return False
            self.update_progress(f"提取人脸: {i + 1}/{total_shots}")
            clip_path = Path(clip_url)
            if not clip_path.exists():
                self.logger.warning(f"找不到片段文件 {clip_path}，跳过镜头 {shot_id}。")
                continue
            face_embeddings = self._extract_face_embeddings(clip_path)
            if face_embeddings:
                batch_size = 20
                total_faces = len(face_embeddings)
                for j in range(0, total_faces, batch_size):
                    batch = face_embeddings[j : j + batch_size]
                    self.logger.debug(f"正在为镜头 {shot_id} 保存一批人脸数据 (数量: {len(batch)} / {total_faces})")
                    with self.db_manager.get_session() as update_session:
                        for face_data in batch:
                            new_face = FaceEmbeddings(
                                shot_id=shot_id,
                                embedding=face_data["embedding"].tobytes(),
                                bounding_box=face_data["bounding_box"],
                                time_offset_in_shot=face_data["time_offset"],
                            )
                            update_session.add(new_face)
        self.logger.info("所有人脸特征提取完成。")
        return True

    def _perform_shot_analysis(
        self, force_level: Optional[str] = None, skip_until: float = 0.0, genre: str = "personal"
    ) -> bool:
        """执行镜头检测、分割和分析（使用scenedetect CLI）"""
        video_info = self.get_video_info()
        if not video_info:
            self.logger.error("无法获取视频信息")
            return False

        input_path_str = video_info.get("input_file_path")
        if not input_path_str or not Path(input_path_str).exists():
            self.logger.error(f"视频文件路径无效或不存在: {input_path_str}")
            return False
        input_video_path = Path(input_path_str)

        clips_dir = settings.CLIPS_DIR
        file_hash = video_info["file_hash"]

        # --- 【核心改动】区分 soft 和 full 的清理逻辑 ---
        if force_level:
            self.logger.info(f"强制模式 '{force_level}': 正在清理旧的分析数据...")
            # 无论是 soft 还是 full，都清理镜头分析结果及后续阶段数据
            self.db_manager.clear_shot_analysis_data(self.video_id)
            with self.db_manager.get_session() as session:
                from database.models import Scenes, StorySequence

                session.query(Scenes).filter_by(video_id=self.video_id).delete()
                session.query(StorySequence).filter_by(video_id=self.video_id).delete()
                self.logger.info("已清理 Shots, Scenes, 和 StorySequence 表中的相关数据。")

            if force_level == "full":
                self.logger.info("强制模式 'full': 正在清理检测到的镜头、视频片段和人脸数据...")
                with self.db_manager.get_session() as session:
                    from database.models import DetectedShots, FaceEmbeddings, Shots

                    # 清理检测到的镜头列表
                    session.query(DetectedShots).filter_by(video_id=self.video_id).delete()
                    # 清理人脸数据 (因为人脸依赖于镜头)
                    subquery = session.query(Shots.id).filter(Shots.video_id == self.video_id).scalar_subquery()
                    session.query(FaceEmbeddings).filter(FaceEmbeddings.shot_id.in_(subquery)).delete(
                        synchronize_session=False
                    )

                # 删除所有相关的视频片段文件
                for f in clips_dir.glob(f"{file_hash}-shot-*.mp4"):
                    f.unlink()
                self.logger.info("已清理 DetectedShots 数据、人脸数据和所有相关的视频片段文件。")
        # --- 清理逻辑结束 ---

        shot_list_from_csv = None
        # full 模式下，不尝试从数据库加载
        if force_level != "full":
            shot_list_from_csv = self.db_manager.get_detected_shots(self.video_id)
            if shot_list_from_csv:
                self.logger.info("从数据库缓存中成功加载已检测到的镜头列表。")

        if not shot_list_from_csv:
            self.logger.info("缓存未命中或强制执行，开始使用 scenedetect CLI 进行处理...")
            video_height = video_info.get("height", 1080)
            shots_csv_path = self._run_scenedetect_cli(input_video_path, clips_dir, file_hash, video_height, skip_until)
            if not shots_csv_path:
                self.logger.error("镜头检测和分割步骤失败。")
                return False
            shot_list_from_csv = self._parse_scenedetect_csv(shots_csv_path)
            if not shot_list_from_csv:
                self.logger.error("解析镜头列表失败或列表为空。")
                return False
            self.db_manager.save_detected_shots(
                self.video_id, shot_list_from_csv, f"detector:{settings.SCENE_DETECTOR}"
            )

        # --- 【核心改动】文件发现与匹配 ---
        self.logger.info("开始发现并匹配磁盘上的镜头片段文件...")
        # 使用 glob 发现所有实际生成的片段文件，并进行自然排序
        discovered_clip_paths = sorted(
            [p for p in clips_dir.glob(f"{file_hash}-shot-*.mp4") if ".lowres" not in p.name]
        )

        # 验证CSV中的镜头数量是否与磁盘上的文件数量匹配
        if len(shot_list_from_csv) != len(discovered_clip_paths):
            self.logger.error(
                f"镜头CSV列表数量 ({len(shot_list_from_csv)}) 与磁盘上发现的片段文件数量 ({len(discovered_clip_paths)}) 不匹配。"
                "这可能意味着 scenedetect 分割视频时出错。请检查 scenedetect 的日志输出。"
            )
            return False

        # --- 准备分析任务 (全新逻辑) ---
        shots_to_analyze = []
        # 将CSV中的时间数据与发现的文件路径进行配对
        for (start_time, end_time, _, _), clip_path in zip(shot_list_from_csv, discovered_clip_paths):
            shot_status = self._get_shot_status(start_time, end_time)
            if force_level in ["soft", "full"] or shot_status != "completed":
                if shot_status == "failed":
                    self.logger.warning(f"镜头 {start_time:.2f}s-{end_time:.2f}s 上次分析失败，将重新尝试。")
                    self._delete_shot_record(start_time, end_time)

                # 我们已经从磁盘上找到了 clip_path，所以它一定是存在的
                shots_to_analyze.append({"start": start_time, "end": end_time, "path": clip_path, "genre": genre})
            else:
                self.logger.debug(f"镜头 {start_time:.2f}s-{end_time:.2f}s 已分析，跳过AI分析。")

        # --- 执行并行分析 (保持不变) ---
        if not shots_to_analyze:
            self.logger.info("没有需要进行AI分析的新镜头。")
        else:
            self.logger.info(f"共准备好 {len(shots_to_analyze)} 个镜头进行AI分析。")
            completed_count, failed_count = 0, 0
            db_writer_thread = threading.Thread(target=self._db_writer_worker, daemon=True)
            db_writer_thread.start()
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
                    future_to_shot = {executor.submit(self._process_single_shot, s): s for s in shots_to_analyze}
                    submitted_count = len(future_to_shot)
                    self.logger.info(f"已提交 {submitted_count} 个分析任务，等待处理完成...")
                    for future in concurrent.futures.as_completed(future_to_shot):  # 注意：这里已修正为 future_to_shot
                        if shutdown_event.is_set():
                            self.logger.warning("检测到关闭信号，正在取消所有排队中的任务...")
                            for f in future_to_shot:
                                f.cancel()  # 注意：这里已修正为 future_to_shot
                            break
                        shot_info = future_to_shot[future]  # 注意：这里已修正为 future_to_shot
                        try:
                            if future.result():
                                completed_count += 1
                            else:
                                failed_count += 1
                        except concurrent.futures.CancelledError:
                            self.logger.debug(f"任务 {shot_info['path'].name} 已被取消。")
                            failed_count += 1
                        except Exception as exc:
                            self.logger.error(
                                f"处理镜头 {shot_info['start']:.2f}s-{shot_info['end']:.2f}s 时产生异常: {exc}"
                            )
                            failed_count += 1
                        progress_info = f"分析中 - 已完成 {completed_count + failed_count}/{submitted_count} (成功: {completed_count}, 失败: {failed_count})"
                        self.update_progress(progress_info)
            finally:
                self.logger.info("分析循环结束，等待数据库写入完成...")
                self._db_queue.join()
                self._stop_db_writer.set()
                db_writer_thread.join()
            self.logger.info(
                f"镜头分析完成。总提交任务数: {len(shots_to_analyze)}，成功: {completed_count}，失败: {failed_count}"
            )
            if failed_count > 0 or shutdown_event.is_set():
                return False
        return True
