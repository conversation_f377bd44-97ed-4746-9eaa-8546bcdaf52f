"""
阶段4：标语生成 (Tagline Generation)
为视频生成一个简洁有力的宣传标语。
"""

from typing import Optional

from config.prompts import TAGLINE_GENERATION_PROMPT
from config.schemas import TaglineResponse
from stages.base import BaseStage
from utils.ai_utils import ai_client


class Stage4TaglineGeneration(BaseStage):
    """阶段4：标语生成"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)

    @property
    def stage_number(self) -> int:
        return 4

    @property
    def stage_name(self) -> str:
        return "标语生成 (Tagline)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage3_status = self.db_manager.get_stage_status(self.video_id, 3)
        if not stage3_status or stage3_status["status"] != "completed":
            return False, "阶段3（外部资料增强）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行标语生成"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的标语数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "tagline")

        # 检查数据库中是否已有输出
        existing_tagline = self.db_manager.get_stage_output(self.video_id, self.stage_number, "tagline")
        if existing_tagline:
            self.logger.info("从数据库加载已缓存的标语数据。")
            return True

        try:
            # 1. 获取研究摘要
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)
            if not research_summary:
                self.logger.error("无法获取研究摘要数据。")
                return False

            # 2. 调用AI生成标语
            self.update_progress("AI正在生成视频标语...")
            prompt = TAGLINE_GENERATION_PROMPT.format(research_summary=research_summary)
            result = ai_client.call_ai_with_tool(prompt, response_model=TaglineResponse)

            if not result or not result.tagline:
                self.logger.error("AI未能生成有效的标语。")
                return False

            # 3. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="tagline",
                output_data=result.model_dump(),
            )

            self.logger.info(f"✅ 成功生成标语: {result.tagline}")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
