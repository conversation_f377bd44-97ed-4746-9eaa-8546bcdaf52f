"""
阶段16：剧本创作 (D2S Rewriter)
第二步：基于大纲和创作策略生成完整剧本
"""

import concurrent.futures
import json
from collections import defaultdict
from typing import Any, Dict, List, Optional, Tuple

from config.prompts import (
    GENRE_INSTRUCTIONS,
    NARRATION_INTERVENTION_INSTRUCTIONS,
    SCENE_SCRIPT_EVALUATION_PROMPT,
    SCENE_SCRIPT_SELF_CORRECTION_PROMPT,
    SCRIPT_EVALUATION_PROMPT,
    SCRIPT_GENERATION_PROMPT,
)
from config.schemas import MovieCommentaryScriptResponse, SceneScriptEvaluationResponse, ScriptEvaluationResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client
from utils.shared_state import shutdown_event


class Stage16D2SRewriter(BaseStage):
    """阶段16：剧本创作 (D2S Rewriter)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.rewriter_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(
                f"已为D2S Rewriter(剧本生成)阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}"
            )
        else:
            self.rewriter_client = ai_client

    @property
    def stage_number(self) -> int:
        return 16

    @property
    def stage_name(self) -> str:
        return "剧本创作 (D2S Rewriter)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage15_status = self.db_manager.get_stage_status(self.video_id, 15)
        if not stage15_status or stage15_status["status"] != "completed":
            return False, "阶段15 (D2S Rewriter 策略规划) 尚未完成"

        script_strategy = self.db_manager.get_stage_output(self.video_id, 15, "script_strategy")
        if not script_strategy:
            return False, "数据库中未找到剧本创作策略数据"

        story_outline_data = self.db_manager.get_stage_output(self.video_id, 13, "story_outline")
        if not story_outline_data:
            return False, "数据库中未找到故事大纲数据"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """根据强制级别，调度剧本的生成或优化工作流。"""
        try:
            existing_script = self.db_manager.get_master_script(self.video_id)

            if force_level == "full":
                self.logger.info("强制模式 'full': 清理旧的旁白文案和评估报告，将从头开始完整生成。")
                self.db_manager.clear_master_script(self.video_id)
                self.db_manager.clear_stage_output(self.video_id, self.stage_number, "script_evaluation")
                return self._run_full_generation_workflow(**kwargs)

            if force_level == "soft":
                self.logger.info("--- 强制软执行模式 ---")
                if existing_script:
                    self.logger.info("发现已存在的剧本，将在此基础上进行评估和优化。")
                    return self._run_refinement_workflow(existing_script, **kwargs)
                else:
                    self.logger.info("未发现已存在的剧本，将从头开始完整生成。")
                    return self._run_full_generation_workflow(**kwargs)

            if existing_script:
                self.logger.info("已存在旁白文案，跳过。")
                return True

            # 默认行为：如果不存在，则从头开始生成
            return self._run_full_generation_workflow(**kwargs)

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _run_full_generation_workflow(self, **kwargs) -> bool:
        """执行完整的剧本生成流程：并行生成每个场景的初稿，然后进行优化。"""
        # 1. 加载所有必需的上下文数据
        story_outline_data, master_context, script_strategy, tagline, dossier_text = self._load_common_context()
        if not all([story_outline_data, master_context, script_strategy]):
            return False
        # Type assertion: after the None check above, story_outline_data is guaranteed to be non-None
        assert story_outline_data is not None
        scenes_to_process = story_outline_data.get("story_outline", [])
        if not scenes_to_process:
            self.logger.error("故事大纲为空，无法生成剧本。")
            return False

        # --- 【核心修改】应用调试模式限制 ---
        if settings.DEBUG_MODE_SCENE_LIMIT > 0:
            self.logger.warning(f"--- 调试模式已激活 --- 将只处理前 {settings.DEBUG_MODE_SCENE_LIMIT} 个场景。")
            scenes_to_process = scenes_to_process[: settings.DEBUG_MODE_SCENE_LIMIT]

        # 2. 并行处理每个场景，生成并优化
        # tasks 列表中的每个元素都是一个 (scene_outline, initial_draft) 元组，initial_draft 在此模式下为 None
        tasks: List[Tuple[Dict[str, Any], Optional[List[Dict[str, Any]]]]] = [
            (scene_outline, None) for scene_outline in scenes_to_process
        ]
        # Type assertions: after the None check above, these are guaranteed to be non-None
        assert master_context is not None
        assert script_strategy is not None
        return self._parallel_process_scenes(
            tasks, master_context, script_strategy, tagline, dossier_text, kwargs, is_refinement_only=False
        )

    def _run_refinement_workflow(self, existing_script: List[Dict[str, Any]], **kwargs) -> bool:
        """仅对已存在的剧本执行评估和优化流程。"""
        # 1. 加载上下文
        story_outline_data, master_context, script_strategy, tagline, dossier_text = self._load_common_context()
        if not all([story_outline_data, master_context, script_strategy]):
            return False

        # 2. 将现有剧本按源场景ID分组
        script_by_scene = defaultdict(list)
        for para in existing_script:
            scene_num = para.get("source_scene_number")
            if scene_num:
                script_by_scene[scene_num].append(para)

        # 3. 准备并行处理的任务列表
        # Type assertions: after the None check above, these are guaranteed to be non-None
        assert story_outline_data is not None
        assert master_context is not None
        assert script_strategy is not None

        scenes_to_process = story_outline_data.get("story_outline", [])

        # --- 【核心修改】应用调试模式限制 ---
        if settings.DEBUG_MODE_SCENE_LIMIT > 0:
            self.logger.warning(f"--- 调试模式已激活 --- 将只处理前 {settings.DEBUG_MODE_SCENE_LIMIT} 个场景。")
            scenes_to_process = scenes_to_process[: settings.DEBUG_MODE_SCENE_LIMIT]

        tasks: List[Tuple[Dict[str, Any], Optional[List[Dict[str, Any]]]]] = []
        for scene_outline in scenes_to_process:
            scene_num = scene_outline["scene_number"]
            initial_draft = script_by_scene.get(scene_num)
            if not initial_draft:
                self.logger.warning(f"在现有剧本中未找到场景 {scene_num} 的内容，将重新生成。")
            tasks.append((scene_outline, initial_draft))  # initial_draft 可能为 None

        # 4. 并行优化
        return self._parallel_process_scenes(
            tasks, master_context, script_strategy, tagline, dossier_text, kwargs, is_refinement_only=True
        )

    def _load_common_context(
        self,
    ) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]], Optional[Dict[str, Any]], str, str]:
        """加载所有工作流共享的上下文数据。"""
        story_outline_data = self.db_manager.get_stage_output(self.video_id, 13, "story_outline")
        master_context = self.db_manager.get_stage_output(self.video_id, 9, "creative_brief")
        script_strategy = self.db_manager.get_stage_output(self.video_id, 15, "script_strategy")
        dossier_data = self.db_manager.get_stage_output(self.video_id, 14, "character_dossier") or {}
        dossier_text = dossier_data.get("text", "无角色档案。")
        tagline_data = self.db_manager.get_stage_output(self.video_id, 4, "tagline") or {}
        tagline = tagline_data.get("tagline", "无")
        return story_outline_data, master_context, script_strategy, tagline, dossier_text

    def _parallel_process_scenes(
        self,
        tasks: List[Tuple[Dict[str, Any], Optional[List[Dict[str, Any]]]]],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        kwargs: Dict[str, Any],
        is_refinement_only: bool,
    ) -> bool:
        """统一的并行处理函数，用于生成或优化场景。"""
        total_scenes = len(tasks)
        completed_count, failed_count = 0, 0
        results_in_order = []

        # Log the workflow type for debugging
        workflow_type = "优化现有剧本" if is_refinement_only else "生成新剧本"
        self.update_progress(f"开始并行处理 {total_scenes} 个场景的剧本 ({workflow_type})...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
            future_to_scene_num = {}
            for scene_outline, initial_draft in tasks:
                future = executor.submit(
                    self._process_single_scene_script,
                    scene_outline,
                    master_context,
                    script_strategy,
                    tagline,
                    dossier_text,
                    kwargs,
                    initial_draft,
                )
                future_to_scene_num[future] = scene_outline.get("scene_number")

            for future in concurrent.futures.as_completed(future_to_scene_num):
                scene_num = future_to_scene_num[future]
                try:
                    if shutdown_event.is_set():
                        for f in future_to_scene_num:
                            f.cancel()
                        break
                    scene_script = future.result()
                    if scene_script:
                        results_in_order.append({"scene_number": scene_num, "script": scene_script})
                        completed_count += 1
                    else:
                        failed_count += 1
                    progress_info = f"处理中 - 已完成 {completed_count + failed_count}/{total_scenes} (成功: {completed_count}, 失败: {failed_count})"
                    self.update_progress(progress_info)
                except Exception as exc:
                    self.logger.error(f"处理场景 {scene_num} 时产生严重异常: {exc}", exc_info=True)
                    failed_count += 1

        if failed_count > 0 or shutdown_event.is_set():
            self.logger.error("一个或多个场景未能成功生成/优化剧本，阶段失败。")
            return False

        # 3. 汇总并最终确定剧本
        final_script_beats = []
        for result in sorted(results_in_order, key=lambda x: x["scene_number"]):
            final_script_beats.extend(result["script"])

        # 4. 【核心修改】执行全局评估
        self._run_global_evaluation(final_script_beats, master_context)

        # 5. 保存最终剧本
        self.db_manager.save_master_script(self.video_id, final_script_beats)
        self.logger.info(f"✅ 完整剧本创作与修正完成，最终版本包含 {len(final_script_beats)} 个音画节拍。")
        return True

    def _process_single_scene_script(
        self,
        scene_outline: Dict[str, Any],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        kwargs: Dict[str, Any],
        initial_draft: Optional[List[Dict[str, Any]]] = None,
    ) -> Optional[List[Dict[str, Any]]]:
        """为单个场景执行完整的“生成-评估-修正”循环。"""
        MAX_REFINEMENT_PASSES = 3
        scene_num = scene_outline.get("scene_number", "未知")
        narration_style_preset = kwargs.get("narration_style_preset", "balanced")

        current_script_paragraphs = initial_draft
        if not current_script_paragraphs:
            self.logger.info(f"开始为场景 {scene_num} 生成初始剧本...")
            current_script_paragraphs = self._generate_script_for_scene(
                scene_outline, master_context, script_strategy, tagline, dossier_text, kwargs
            )
            if not current_script_paragraphs:
                self.logger.error(f"场景 {scene_num} 的初始剧本生成失败。")
                return None

        for i in range(MAX_REFINEMENT_PASSES):
            self.logger.info(f"开始为场景 {scene_num} 进行第 {i + 1}/{MAX_REFINEMENT_PASSES} 轮评估...")
            evaluation = self._evaluate_scene_script(scene_outline, current_script_paragraphs, narration_style_preset)
            if not evaluation:
                self.logger.warning(f"场景 {scene_num} 的评估失败，将使用当前版本。")
                break
            self.logger.info(
                f"场景 {scene_num} 评估得分: {evaluation.score}/5。修改建议: {evaluation.suggested_improvements}"
            )
            if evaluation.is_ready_for_production:  # 使用 is_ready_for_production 判断是否达标
                self.logger.info(f"场景 {scene_num} 质量达标，无需修正。")
                break
            self.logger.info(f"场景 {scene_num} 质量未达标，开始第 {i + 1}/{MAX_REFINEMENT_PASSES} 轮修正...")
            refined_script = self._refine_scene_script(scene_outline, current_script_paragraphs, evaluation)
            if refined_script:
                current_script_paragraphs = refined_script
            else:
                self.logger.error(f"场景 {scene_num} 的修正失败，将保留上一版。")
                break
        else:
            self.logger.warning(f"场景 {scene_num} 已达到最大修正次数，将使用最终版本。")
        return current_script_paragraphs

    def _generate_script_for_scene(
        self,
        scene_outline: Dict[str, Any],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        kwargs: Dict[str, Any],
    ) -> Optional[List[Dict[str, Any]]]:
        """为单个场景生成音画节拍列表。"""

        try:
            # --- 从参数中提取所需信息 ---
            global_narration_tone = script_strategy.get("global_narration_tone", "未指定")
            narration_style_preset = kwargs.get("narration_style_preset", "balanced")
            genre = kwargs.get("type", "drama")

            # 2. 准备提示词的各个部分
            prompt = SCRIPT_GENERATION_PROMPT.format(
                tagline=tagline,
                global_narration_tone=global_narration_tone,
                genre_instruction=GENRE_INSTRUCTIONS.get(genre, GENRE_INSTRUCTIONS["drama"]),
                narration_intervention_instruction=NARRATION_INTERVENTION_INSTRUCTIONS.get(
                    narration_style_preset, NARRATION_INTERVENTION_INSTRUCTIONS["balanced"]
                ),
                characters_json=json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2),
                dossier_text=dossier_text,
                full_script_strategy_json=json.dumps(script_strategy, ensure_ascii=False, indent=2),
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                language=settings.NARRATION_LANGUAGE,
            )

            # 3. 调用AI生成剧本
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=MovieCommentaryScriptResponse)

            if result and result.script:
                # 为每个生成的节拍注入源场景编号和节拍编号
                scene_num_val = scene_outline.get("scene_number")
                if scene_num_val is None:
                    self.logger.error(f"场景大纲缺少必需的 'scene_number' 字段: {scene_outline}")
                    return None
                for i, beat in enumerate(result.script):
                    beat.source_scene_number = scene_num_val
                    beat.beat_number = i + 1  # 确保节拍编号从1开始
                return [beat.model_dump() for beat in result.script]
            else:
                self.logger.warning(f"AI未能为场景 {scene_outline.get('scene_number')} 生成有效的剧本内容。")
                return None
        except Exception as e:
            self.logger.error(f"为场景 {scene_outline.get('scene_number')} 生成剧本时发生严重错误: {e}", exc_info=True)
            return None

    def _evaluate_scene_script(
        self, scene_outline: Dict[str, Any], script_beats: List[Dict[str, Any]], narration_style_preset: str
    ) -> Optional[SceneScriptEvaluationResponse]:
        """对单个场景的剧本进行评估，并传入基于音频时长的旁白策略分析。"""
        try:
            # --- 【核心修改】计算旁白预估时长 (现在从 ScriptBeat 中提取 audio_content) ---
            narration_text = " ".join(
                beat["audio_content"]
                for beat in script_beats
                if beat.get("audio_content") and beat.get("narration_type") in ["NARRATOR", "INNER_MONOLOGUE"]
            )
            estimated_narration_duration = (
                len(narration_text) / settings.NARRATION_CHAR_PER_SEC if settings.NARRATION_CHAR_PER_SEC > 0 else 0
            )

            # --- 【核心修改】获取并计算场景中所有镜头的原声对话总时长 ---
            candidate_ids_str = scene_outline.get("candidate_clip_ids", [])
            candidate_order_ids = []
            scene_numbers_to_resolve = []
            for cid_str in candidate_ids_str:
                if isinstance(cid_str, str) and cid_str.startswith("scene_"):
                    try:
                        scene_numbers_to_resolve.append(int(cid_str.split("_")[1]))
                    except (ValueError, IndexError):
                        pass
                else:
                    try:
                        candidate_order_ids.append(int(cid_str))
                    except (ValueError, TypeError):
                        pass
            if scene_numbers_to_resolve:
                resolved_ids = set(candidate_order_ids)
                for s_num in scene_numbers_to_resolve:
                    shots = self.db_manager.get_shots_by_scene_number(self.video_id, s_num)
                    for shot in shots:
                        if shot.get("shot_order_id") is not None:
                            resolved_ids.add(shot["shot_order_id"])
                candidate_order_ids = sorted(list(resolved_ids))

            candidate_shots = self.db_manager.get_shots_by_order_ids(self.video_id, candidate_order_ids)

            # 估算原声对话时长：简单地将所有有对话的镜头的时长相加
            # 这是一个合理的近似，因为在 balanced 策略下，我们期望剧本中的对话能对应到这些镜头
            total_dialogue_duration = sum(
                shot["end_time"] - shot["start_time"] for shot in candidate_shots if shot.get("dialogue")
            )

            # --- 【核心修改】构建基于音频时长的统计数据 ---
            total_audio_duration = estimated_narration_duration + total_dialogue_duration
            if total_audio_duration > 0:
                narration_ratio = estimated_narration_duration / total_audio_duration
                stats_text = (
                    f"旁白预估总时长: {estimated_narration_duration:.2f} 秒\n"
                    f"镜头原声对话总时长: {total_dialogue_duration:.2f} 秒\n"
                    f"旁白在总音轨中的时间占比: {narration_ratio:.1%}"
                )
            else:
                stats_text = "场景中无可用的音频素材时长。"

            narration_instruction = NARRATION_INTERVENTION_INSTRUCTIONS.get(narration_style_preset, "")
            # script_beats_json_compatible 已经是 List[Dict[str, Any]]，可以直接序列化
            # 无需移除 source_scene_number 或 beat_number，因为它们是 ScriptBeat 的必需字段

            prompt = SCENE_SCRIPT_EVALUATION_PROMPT.format(
                narration_style_instruction=narration_instruction,
                script_statistics_text=stats_text,  # 传入包含音频时长统计的提示词
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                generated_script_json=json.dumps(script_beats, ensure_ascii=False, indent=2),  # 直接使用 script_beats
            )
            return self.rewriter_client.call_ai_with_tool(prompt, response_model=SceneScriptEvaluationResponse)
        except Exception as e:
            self.logger.error(f"场景 {scene_outline.get('scene_number')} 的内部剧本评估失败: {e}", exc_info=True)
            return None

    def _refine_scene_script(
        self,
        scene_outline: Dict[str, Any],
        original_script: List[Dict[str, Any]],  # original_script 现在是 List[Dict[str, Any]]，代表 ScriptBeat 列表
        evaluation: SceneScriptEvaluationResponse,
    ) -> Optional[List[Dict[str, Any]]]:
        """根据评估报告修正单个场景的音画节拍。"""
        try:
            # original_script_json_compatible 已经是 List[Dict[str, Any]]，可以直接序列化
            # 但我们需要确保 'source_scene_number' 和 'beat_number' 字段存在，因为它们是 ScriptBeat 的必需字段
            # 在这里，我们假设 original_script 已经包含这些字段

            prompt = SCENE_SCRIPT_SELF_CORRECTION_PROMPT.format(
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                original_script_json=json.dumps(
                    original_script, ensure_ascii=False, indent=2
                ),  # 直接使用 original_script
                evaluation_report_json=json.dumps(evaluation.model_dump(), ensure_ascii=False, indent=2),
                language=settings.NARRATION_LANGUAGE,
            )
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=MovieCommentaryScriptResponse)

            if result and result.script:
                scene_num = scene_outline.get("scene_number")
                if scene_num is None:
                    self.logger.error(f"场景大纲缺少必需的 'scene_number' 字段: {scene_outline}")
                    return None
                for i, beat in enumerate(result.script):
                    beat.source_scene_number = scene_num
                    beat.beat_number = i + 1  # 重新编号
                return [beat.model_dump() for beat in result.script]
            else:
                self.logger.warning(f"AI未能为场景 {scene_outline.get('scene_number')} 生成有效的修正剧本内容。")
                return None
        except Exception as e:
            self.logger.error(f"场景 {scene_outline.get('scene_number')} 的AI剧本修正失败: {e}", exc_info=True)
            return None

    def _run_global_evaluation(self, script_beats: List[Dict[str, Any]], master_context: Dict[str, Any]):
        """在所有场景生成完毕后，对完整剧本进行一次全局评估。"""
        self.update_progress("正在对完整剧本进行最终的全局质量评估...")
        try:
            script_text = ""
            for i, beat in enumerate(script_beats):
                script_text += f"\n--- 节拍 {i + 1} (场景 {beat['source_scene_number']}) ---\n"
                script_text += f"视觉: {beat['visual_description']}\n"
                if beat.get("audio_content"):
                    script_text += f"音频 ({beat['narration_type']}): {beat['audio_content']}\n"

            prompt = SCRIPT_EVALUATION_PROMPT.format(
                project_info_json=json.dumps(master_context.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2),
                script_text=script_text.strip(),
            )
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=ScriptEvaluationResponse)
            if result:
                self.db_manager.save_stage_output(
                    video_id=self.video_id,
                    stage_number=self.stage_number,
                    stage_name=self.stage_name,
                    output_type="script_evaluation",
                    output_data=result.model_dump(),
                )
                self.logger.info("✅ 成功生成并保存了全局剧本评估报告。")
            else:
                self.logger.error("AI未能返回任何全局评估结果。")
        except Exception as e:
            self.logger.error(f"执行全局剧本评估时出错: {e}", exc_info=True)
