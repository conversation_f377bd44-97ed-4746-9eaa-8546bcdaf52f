"""
阶段10：D2S 读者模块 (Reader)
第一步：事件识别
"""

import json
from typing import Optional

from config.prompts import EVENT_IDENTIFICATION_PROMPT
from config.schemas import EventIdentificationResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client


class Stage10Reader(BaseStage):
    """阶段10：D2S 读者模块 - 事件识别"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        # 此阶段的核心是AI解析，可以使用高级AI客户端
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.reader_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(f"已为D2S Reader阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}")
        else:
            self.reader_client = ai_client

    @property
    def stage_number(self) -> int:
        return 10

    @property
    def stage_name(self) -> str:
        return "D2S Reader (事件识别)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖于阶段9（数据基础构建）完成
        stage9_status = self.db_manager.get_stage_status(self.video_id, 9)
        if not stage9_status or stage9_status["status"] != "completed":
            return False, "阶段9 (数据基础构建) 尚未完成"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行事件识别"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的叙事事件数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "narrative_events")

        # 检查数据库中是否已有输出
        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "narrative_events")
        if existing_output:
            self.logger.info("从数据库加载已缓存的叙事事件数据。")
            return True

        # 1. 从数据库获取所有场景的摘要信息
        self.update_progress("从数据库加载场景摘要...")
        scene_summaries = self.db_manager.get_all_scene_summaries_for_event_detection(self.video_id)
        if not scene_summaries:
            self.logger.error("数据库中没有找到任何场景摘要，无法进行事件识别。")
            return False

        # 2. 调用AI进行事件识别
        self.update_progress("AI正在基于场景摘要识别叙事事件...")
        try:
            prompt = EVENT_IDENTIFICATION_PROMPT.format(
                scene_summaries_json=json.dumps(scene_summaries, ensure_ascii=False, indent=2),
                language=settings.NARRATION_LANGUAGE,
            )

            result = self.reader_client.call_ai_with_tool(prompt, response_model=EventIdentificationResponse)

            if not result or not result.narrative_events:
                self.logger.error("AI未能识别出任何叙事事件。")
                return False

            # 3. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="narrative_events",
                output_data=result.model_dump(),
            )

            self.logger.info(f"✅ 成功识别并保存 {len(result.narrative_events)} 个叙事事件到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
