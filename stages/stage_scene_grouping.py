"""
阶段7：场景聚合 (Shots to Scenes)
将微观的镜头(Shots)聚合成具有叙事意义的场景(Scenes)。
"""

from typing import Any, Dict, List, Optional

from sqlalchemy.orm import joinedload

from config.prompts import ORPHAN_SHOT_FIX_PROMPT, SCENE_GROUPING_PROMPT
from config.schemas import OrphanFixDecision, SceneGroupingResponse
from config.settings import settings
from database.models import Scenes, Shots
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client


class Stage7SceneGrouping(BaseStage):
    """阶段7：场景聚合"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.grouping_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(f"已为场景聚合阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}")
        else:
            self.logger.debug("未提供完整的高级AI配置，场景聚合阶段将使用默认的全局AI客户端。")
            self.grouping_client = ai_client

    @property
    def stage_number(self) -> int:
        return 7  # ← 原 6 +1

    @property
    def stage_name(self) -> str:
        return "场景聚合 (镜头->场景)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage6_status = self.db_manager.get_stage_status(self.video_id, 6)
        if not stage6_status or stage6_status["status"] != "completed":
            return False, "阶段6（角色自动标注）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行场景聚合，采用动态分块策略以提高边界准确性。"""
        # --- 【核心改动】新增：检查阶段是否已完成，并据此决定soft模式的行为 ---
        status_record = self.db_manager.get_stage_status(self.video_id, self.stage_number)
        is_already_completed = status_record and status_record.get("status") == "completed"

        # --- 新的 soft force 逻辑 ---
        if force_level == "soft" and is_already_completed:
            self.logger.info("--- 强制软执行模式 ---")
            self.logger.info(f"阶段{self.stage_number}已完成，将仅检查并修复孤儿镜头。")

            # 确保所有镜头都有顺序ID，以防万一
            shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
            if not any(shot.get("shot_order_id") for shot in shots):
                self.logger.info("镜头缺少顺序ID，正在重新分配...")
                self._assign_shot_order_ids(shots)

            orphan_shots = self._get_orphan_shots()
            if not orphan_shots:
                self.logger.info("验证通过，没有发现孤儿镜头。")
                return True

            self.logger.warning(f"发现 {len(orphan_shots)} 个孤儿镜头，开始修复流程...")
            self._fix_orphan_shots(orphan_shots)
            self.logger.info("孤儿镜头修复流程完成。")
            return True

        # --- 原有的完整执行逻辑（适用于首次运行或 --force full）---
        # 只要不是 "soft force on completed"，就执行完整流程
        if force_level:
            self._clear_data()

        shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
        shots.sort(key=lambda x: x["start_time"])
        if not shots:
            self.logger.info("没有已分析的镜头(Shots)可供聚合，跳过。")
            return True

        self._assign_shot_order_ids(shots)
        # ... (后续的完整聚合逻辑保持不变)
        self.update_progress(f"开始将 {len(shots)} 个镜头动态聚合为场景...")

        CHUNK_SIZE = 100
        all_raw_scenes = []
        shot_order_id_to_index_map = {shot["shot_order_id"]: i for i, shot in enumerate(shots)}
        start_index = 0
        chunk_num = 0

        while start_index < len(shots):
            chunk_num += 1
            chunk_shots = shots[start_index : start_index + CHUNK_SIZE]
            if not chunk_shots:
                break

            self.update_progress(f"处理动态镜头块 {chunk_num} (从镜头索引 {start_index} 开始)...")
            self.logger.info(f"正在处理从索引 {start_index} 开始的 {len(chunk_shots)} 个镜头...")

            # --- 对话显示逻辑 (保持不变) ---
            shot_descriptions = []
            last_dialogue = None
            for shot in chunk_shots:
                dialogue_text = (shot.get("dialogue") or "").strip()
                dialogue_display = ""

                if dialogue_text:
                    if dialogue_text == last_dialogue:
                        dialogue_display = "对白: (同上)"
                    else:
                        dialogue_display = f"对白: {dialogue_text}"
                        last_dialogue = dialogue_text
                else:
                    dialogue_display = "对白: (无)"
                    last_dialogue = None

                shot_descriptions.append(
                    (
                        f"Shot {shot['shot_order_id']} ({shot['start_time']:.1f}s-{shot['end_time']:.1f}s): "
                        f"环境: {shot.get('setting', '')}, 人物描述: {shot.get('people', '')}, "
                        f"已识别角色: {shot.get('character_names_str') or '无'}, "
                        f"动作: {shot.get('action', '')}, 画面文字: {shot.get('on_screen_text', '')}, "
                        f"{dialogue_display}"
                    )
                )

            prompt = SCENE_GROUPING_PROMPT.format(shot_descriptions="\n".join(shot_descriptions))
            prompt = (
                "重要提示：在你的回答中，请使用我们提供的'Shot'后面的数字（例如 Shot 1, Shot 2）作为 start_shot_id 和 end_shot_id。\n\n"
                + prompt
            )

            try:
                chunk_result = self.grouping_client.call_ai_with_tool(prompt, response_model=SceneGroupingResponse)
                raw_chunk_scenes = [scene.model_dump() for scene in chunk_result.scenes]

                chunk_scenes = []
                for scene_dict in raw_chunk_scenes:
                    start_order_id = scene_dict.get("start_shot_id")
                    end_order_id = scene_dict.get("end_shot_id")

                    if start_order_id is None or end_order_id is None or start_order_id > end_order_id:
                        self.logger.warning(f"AI返回了无效的场景顺序ID范围: {start_order_id}-{end_order_id}，已跳过。")
                        continue

                    scene_shots = [s for s in chunk_shots if start_order_id <= s["shot_order_id"] <= end_order_id]
                    if not scene_shots:
                        continue

                    scene_dict["shot_ids"] = [s["id"] for s in scene_shots]
                    chunk_scenes.append(scene_dict)

                all_raw_scenes.extend(chunk_scenes)

                if not chunk_scenes:
                    self.logger.warning("当前块未返回任何场景，将按固定步长前进。")
                    start_index += CHUNK_SIZE
                    continue

                current_last_shot_index = start_index + len(chunk_shots) - 1
                if current_last_shot_index >= len(shots) - 1:
                    self.logger.info("已处理到最后一个镜头，聚合循环结束。")
                    break

                last_scene = chunk_scenes[-1]
                second_last_scene = chunk_scenes[-2] if len(chunk_scenes) > 1 else None
                num_shots_in_last_scene = len(last_scene.get("shot_ids", []))

                if num_shots_in_last_scene <= 5 and second_last_scene:
                    start_shot_order_id = min(
                        s["shot_order_id"] for s in chunk_shots if s["id"] in second_last_scene["shot_ids"]
                    )
                    self.logger.info(
                        f"最后一个场景镜头数({num_shots_in_last_scene})<=5，从倒数第二个场景(起始顺序ID: {start_shot_order_id})开始下一轮。"
                    )
                else:
                    start_shot_order_id = min(
                        s["shot_order_id"] for s in chunk_shots if s["id"] in last_scene["shot_ids"]
                    )
                    self.logger.info(
                        f"最后一个场景镜头数({num_shots_in_last_scene})>5或它是唯一场景，从该场景(起始顺序ID: {start_shot_order_id})开始下一轮。"
                    )

                next_start_index = shot_order_id_to_index_map.get(start_shot_order_id)
                if next_start_index is None or next_start_index <= start_index:
                    self.logger.warning(
                        f"无法确定有效的下一个起始索引(计算出: {next_start_index})，将按固定步长前进以避免死循环。"
                    )
                    start_index += CHUNK_SIZE
                else:
                    start_index = next_start_index

            except Exception as e:
                self.logger.error(f"AI场景聚合失败 (块 {chunk_num}): {e}。将按固定步长前进。", exc_info=True)
                start_index += CHUNK_SIZE
                continue

        try:
            final_scenes = self._merge_scenes(all_raw_scenes, shots)
            self._save_grouped_scenes(final_scenes, shots)
            self.logger.info(f"第一次聚合完成，生成了 {len(final_scenes)} 个场景。")

            self.update_progress("验证并修复孤儿镜头...")
            orphan_shots = self._get_orphan_shots()
            if not orphan_shots:
                self.logger.info("验证通过，没有发现孤儿镜头。")
                return True

            self.logger.warning(f"发现 {len(orphan_shots)} 个孤儿镜头，开始二次修复流程...")
            self._fix_orphan_shots(orphan_shots)
            self.logger.info("场景聚合及修复流程全部完成。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _assign_shot_order_ids(self, shots: List[Dict[str, Any]]):
        """为所有镜头分配并持久化一个从1开始的顺序ID。"""
        self.logger.info(f"正在为 {len(shots)} 个镜头分配顺序ID...")
        updates = []
        for i, shot in enumerate(shots):
            order_id = i + 1
            shot["shot_order_id"] = order_id
            updates.append({"id": shot["id"], "shot_order_id": order_id})

        if updates:
            with self.db_manager.get_session() as session:
                session.bulk_update_mappings(Shots.__mapper__, updates)
            self.logger.info("镜头顺序ID分配并保存完成。")

    def _clear_data(self):
        """清理此阶段及后续阶段的数据"""
        self.logger.info("强制模式: 正在清理场景聚合及后续序列分析的数据...")
        with self.db_manager.get_session() as session:
            from database.models import StorySequence

            session.query(StorySequence).filter_by(video_id=self.video_id).delete()
            session.query(Scenes).filter_by(video_id=self.video_id).delete()
            session.query(Shots).filter_by(video_id=self.video_id).update({"scene_id": None})

    def _merge_scenes(self, raw_scenes: List[Dict[str, Any]], all_shots: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并来自动态块的场景列表，并解决重复问题。"""
        if not raw_scenes:
            return []
        self.logger.info(f"开始合并 {len(raw_scenes)} 个原始场景分组...")

        shot_to_scene_map: Dict[int, Dict[str, Any]] = {}
        for scene in raw_scenes:
            for shot_id in scene.get("shot_ids", []):
                shot_to_scene_map[shot_id] = scene

        unique_scenes_dict: Dict[int, Dict[str, Any]] = {id(s): s for s in shot_to_scene_map.values()}
        unique_scenes = list(unique_scenes_dict.values())

        shots_map = {shot["id"]: shot for shot in all_shots}

        def get_scene_start_time(scene: Dict[str, Any]) -> float:
            shot_ids = scene.get("shot_ids")
            if not shot_ids:
                return float("inf")
            first_shot_id = shot_ids[0]
            first_shot_info = shots_map.get(first_shot_id)
            if first_shot_info:
                return first_shot_info.get("start_time", float("inf"))
            self.logger.warning(f"无法找到场景的第一个镜头ID {first_shot_id} 的信息，该场景将被排到最后。")
            return float("inf")

        final_scenes = sorted(unique_scenes, key=get_scene_start_time)
        self.logger.info(f"合并后得到 {len(final_scenes)} 个唯一的最终场景。")
        return final_scenes

    def _get_orphan_shots(self) -> List[Dict[str, Any]]:
        """从数据库中查询所有 scene_id 为 NULL 的镜头，并按时间排序。"""
        all_shots = self.db_manager.get_shots_with_character_names_for_scene_grouping(self.video_id)
        orphan_shots = [shot for shot in all_shots if shot.get("scene_id") is None]
        orphan_shots.sort(key=lambda x: x["start_time"])
        return orphan_shots

    def _fix_orphan_shots(self, orphan_shots: List[Dict[str, Any]]):
        """使用新的智能修复逻辑处理孤儿镜头。"""
        if not orphan_shots:
            return

        with self.db_manager.get_session() as session:
            # 【核心改动】: 将所有操作都放在同一个会话中

            # 步骤1: 获取所有场景并预加载其镜头
            all_scenes = (
                session.query(Scenes)
                .options(joinedload(Scenes.shots))
                .filter_by(video_id=self.video_id)
                .order_by(Scenes.scene_number)
                .all()
            )
            if not all_scenes:
                self.logger.warning("无法获取已聚合的场景，无法修复孤儿镜头。")
                return

            # 步骤2: 构建 镜头顺序ID -> 场景对象 的映射
            # 因为所有对象都在同一个会话中，所以可以安全地访问 .shots
            order_id_to_scene_map = {
                shot.shot_order_id: scene
                for scene in all_scenes
                for shot in scene.shots
                if shot.shot_order_id is not None
            }

            # 步骤3: 遍历并修复孤儿镜头
            for orphan_shot_data in orphan_shots:
                orphan_order_id = orphan_shot_data.get("shot_order_id")
                if orphan_order_id is None:
                    self.logger.warning(f"孤儿镜头 ID {orphan_shot_data['id']} 缺少顺序ID，无法修复。")
                    continue

                self.update_progress(f"修复孤儿镜头 (顺序ID: {orphan_order_id})...")
                preceding_scene_obj = order_id_to_scene_map.get(orphan_order_id - 1)
                succeeding_scene_obj = order_id_to_scene_map.get(orphan_order_id + 1)

                if not preceding_scene_obj or not succeeding_scene_obj:
                    self.logger.warning(
                        f"孤儿镜头 (顺序ID: {orphan_order_id}) 缺少前置或后置场景，无法自动修复，跳过。"
                    )
                    continue

                if preceding_scene_obj.id == succeeding_scene_obj.id:
                    shot_to_update = session.query(Shots).filter_by(id=orphan_shot_data["id"]).first()
                    if shot_to_update:
                        shot_to_update.scene_id = preceding_scene_obj.id
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入其前后连接的同一个场景 (ID: {preceding_scene_obj.id})。"
                        )
                        order_id_to_scene_map[orphan_order_id] = preceding_scene_obj
                    continue

                def format_shot_desc(shot_obj):
                    return (
                        f"Shot {shot_obj.shot_order_id} ({shot_obj.start_time:.1f}s-{shot_obj.end_time:.1f}s): "
                        f"环境: {shot_obj.setting}, 动作: {shot_obj.action}, 对白: {shot_obj.dialogue}"
                    )

                orphan_shot_obj = session.query(Shots).filter_by(id=orphan_shot_data["id"]).one_or_none()
                if not orphan_shot_obj:
                    self.logger.error(f"数据库中未找到孤儿镜头 {orphan_shot_data['id']}，跳过。")
                    continue

                # 确保前后场景对象也附加到当前会话中
                session.add(preceding_scene_obj)
                session.add(succeeding_scene_obj)

                def get_sort_key(shot_obj):
                    return shot_obj.shot_order_id if shot_obj.shot_order_id is not None else float("inf")

                preceding_shots_desc = "\n".join(
                    [format_shot_desc(s) for s in sorted(preceding_scene_obj.shots, key=get_sort_key)]
                )
                orphan_shot_desc = format_shot_desc(orphan_shot_obj)
                succeeding_shots_desc = "\n".join(
                    [format_shot_desc(s) for s in sorted(succeeding_scene_obj.shots, key=get_sort_key)]
                )

                prompt = ORPHAN_SHOT_FIX_PROMPT.format(
                    preceding_scene_shots=preceding_shots_desc,
                    orphan_shot=orphan_shot_desc,
                    succeeding_scene_shots=succeeding_shots_desc,
                )

                try:
                    decision_result = self.grouping_client.call_ai_with_tool(prompt, response_model=OrphanFixDecision)
                    decision = decision_result.decision
                    target_scene = None
                    if decision == "preceding":
                        target_scene = preceding_scene_obj
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入前一个场景 (编号: {target_scene.scene_number})。"
                        )
                    elif decision == "succeeding":
                        target_scene = succeeding_scene_obj
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已并入后一个场景 (编号: {target_scene.scene_number})。"
                        )

                    if target_scene:
                        orphan_shot_obj.scene_id = target_scene.id
                        order_id_to_scene_map[orphan_order_id] = target_scene
                    elif decision == "new_scene":
                        new_scene = Scenes(
                            video_id=self.video_id,
                            scene_number=-1,  # 临时编号，后续会重新计算
                            start_time=orphan_shot_obj.start_time,
                            end_time=orphan_shot_obj.end_time,
                            summary=f"孤儿镜头 {orphan_shot_obj.shot_order_id} 独立形成的场景。",
                            narrative_purpose="孤立的叙事单元",
                            status="completed",
                        )
                        session.add(new_scene)
                        orphan_shot_obj.scene = new_scene
                        session.flush()
                        self.logger.info(
                            f"决策: 镜头 (顺序ID: {orphan_order_id}) 已自成一个新场景 (临时DB ID: {new_scene.id})。"
                        )
                        order_id_to_scene_map[orphan_order_id] = new_scene
                    else:
                        self.logger.warning(
                            f"AI为孤儿镜头 (顺序ID: {orphan_order_id}) 返回了无效决策: {decision}，跳过。"
                        )
                except Exception as e:
                    self.logger.error(f"修复孤儿镜头 (顺序ID: {orphan_order_id}) 时AI决策失败: {e}", exc_info=True)
                    continue

    def _save_grouped_scenes(self, scenes_data: List[Dict[str, Any]], all_shots: List[Dict[str, Any]]):
        """将AI返回的场景分组数据存入数据库。"""
        shots_map = {shot["id"]: shot for shot in all_shots}
        with self.db_manager.get_session() as session:
            for i, scene_info in enumerate(scenes_data):
                shot_ids = scene_info.get("shot_ids", [])
                if not shot_ids:
                    continue

                contained_shots = [shots_map[sid] for sid in shot_ids if sid in shots_map]
                if not contained_shots:
                    self.logger.warning(f"场景 {i + 1} 的所有镜头ID均无效，跳过保存。")
                    continue

                start_time = min(s["start_time"] for s in contained_shots)
                end_time = max(s["end_time"] for s in contained_shots)

                new_scene = Scenes(
                    video_id=self.video_id,
                    scene_number=i + 1,
                    start_time=start_time,
                    end_time=end_time,
                    summary=scene_info.get("summary"),
                    narrative_purpose=scene_info.get("narrative_purpose"),
                    status="completed",
                )
                session.add(new_scene)

                for shot_id in shot_ids:
                    shot_to_update = session.query(Shots).filter_by(id=shot_id).first()
                    if shot_to_update:
                        shot_to_update.scene = new_scene
                    else:
                        self.logger.warning(f"在数据库中未找到ID为 {shot_id} 的镜头，无法关联到场景 {i + 1}。")

            self.logger.info(f"已将 {len(scenes_data)} 个聚合场景及其关联的镜头信息保存到数据库。")
