"""
阶段15：D2S 重写者模块 (Rewriter)
第一步：剧本创作策略规划
"""

import json
from typing import Optional

from config.prompts import SCRIPT_WRITING_STRATEGY_PROMPT
from config.schemas import ScriptWritingStrategyResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import AIClient, ai_client


class Stage15ScriptStrategy(BaseStage):
    """阶段15：剧本创作策略规划"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        advanced_ai_config = settings.get_advanced_ai_config()
        if all(
            [advanced_ai_config.get("api_keys"), advanced_ai_config.get("base_url"), advanced_ai_config.get("model")]
        ):
            self.strategy_client = AIClient(
                api_keys=advanced_ai_config["api_keys"],
                base_url=advanced_ai_config["base_url"],
                model=advanced_ai_config["model"],
                timeout=advanced_ai_config["timeout"],
            )
            self.logger.debug(f"已为剧本策略阶段初始化专用的高级AI客户端，模型: {advanced_ai_config['model']}")
        else:
            self.strategy_client = ai_client

    @property
    def stage_number(self) -> int:
        return 15

    @property
    def stage_name(self) -> str:
        return "D2S Rewriter (策略规划)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖于阶段13（故事大纲生成）的完成
        stage13_status = self.db_manager.get_stage_status(self.video_id, 13)
        if not stage13_status or stage13_status["status"] != "completed":
            return False, "阶段13 (D2S Rewriter 大纲生成) 尚未完成"

        story_outline_data = self.db_manager.get_stage_output(self.video_id, 13, "story_outline")
        if not story_outline_data:
            return False, "数据库中未找到故事大纲数据"

        # 依赖于阶段14 (角色档案) 完成
        dossier_status = self.db_manager.get_stage_status(self.video_id, 14)
        if not dossier_status or dossier_status["status"] != "completed":
            return False, "阶段14（角色档案生成）尚未完成"

        # 依赖于阶段9 (数据基础构建) 完成以获取 master_context
        stage9_status = self.db_manager.get_stage_status(self.video_id, 9)
        if not stage9_status or stage9_status["status"] != "completed":
            return False, "阶段9 (数据基础构建) 尚未完成"

        creative_brief = self.db_manager.get_stage_output(self.video_id, 9, "creative_brief")
        if not creative_brief:
            return False, "数据库中未找到创作说明书数据"

        if not self.db_manager.get_all_research_summaries(self.video_id):
            return False, "数据库中未找到研究资料摘要"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行剧本创作策略的生成"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的剧本创作策略数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "script_strategy")

        # 检查数据库中是否已有输出
        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "script_strategy")
        if existing_output:
            self.logger.info("从数据库加载已缓存的剧本创作策略数据。")
            return True

        # 1. 从数据库加载所需数据
        try:
            story_outline_data = self.db_manager.get_stage_output(self.video_id, 13, "story_outline")
            master_context = self.db_manager.get_stage_output(self.video_id, 9, "creative_brief")
            research_summary = self.db_manager.get_all_research_summaries(self.video_id)
            dossier_data = self.db_manager.get_stage_output(self.video_id, 14, "character_dossier") or {}
            dossier_text = dossier_data.get("text", "无角色档案。")
            tagline_data = self.db_manager.get_stage_output(self.video_id, 4, "tagline") or {}
            tagline = tagline_data.get("tagline", "无")

            dossier_status = self.db_manager.get_stage_status(self.video_id, 14)
            if not dossier_status or dossier_status["status"] != "completed":
                self.logger.error("前置检查失败：阶段14（角色档案生成）尚未完成。")
                return False
            if not story_outline_data or not master_context:
                self.logger.error("无法从数据库加载前置阶段的输出数据。")
                return False
            if not research_summary or research_summary == "没有可用的研究资料。":
                self.logger.error("无法从数据库加载研究资料摘要。")
                return False

        except Exception as e:
            self.logger.error(f"无法加载前置数据: {e}")
            return False

        # 2. 调用AI生成创作策略
        self.update_progress("AI正在规划全局剧本创作策略...")
        try:
            prompt = SCRIPT_WRITING_STRATEGY_PROMPT.format(
                tagline=tagline,
                dossier_text=dossier_text,
                research_summary=research_summary,
                story_outline_json=json.dumps(story_outline_data, ensure_ascii=False, indent=2),
                project_info_json=json.dumps(master_context.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters"), ensure_ascii=False, indent=2),
                language=settings.NARRATION_LANGUAGE,
            )

            result = self.strategy_client.call_ai_with_tool(prompt, response_model=ScriptWritingStrategyResponse)

            if not result:
                self.logger.error("AI未能生成任何有效的创作策略。")
                return False

            # 3. 保存到数据库
            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="script_strategy",
                output_data=result.model_dump(),
            )

            self.logger.info("✅ 成功生成剧本创作策略并保存到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
