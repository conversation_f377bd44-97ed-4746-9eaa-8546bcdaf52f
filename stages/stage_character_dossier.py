from typing import Optional, Dict, Any
from stages.base import BaseStage
from config.settings import settings
from utils.ai_utils import AI<PERSON><PERSON>, ai_client
from config.prompts import CHARACTER_DOSSIER_REFINE_PROMPT


class Stage14CharacterDossier(BaseStage):
    """阶段14：角色档案生成"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        cfg = settings.get_advanced_ai_config()
        self.client = (
            AIClient(api_keys=cfg["api_keys"], base_url=cfg["base_url"], model=cfg["model"], timeout=cfg["timeout"])
            if cfg["api_keys"]
            else ai_client
        )

    @property
    def stage_number(self) -> int:
        return 14

    @property
    def stage_name(self) -> str:
        return "角色档案生成"

    def check_prerequisites(self) -> tuple[bool, str]:
        # 依赖：6(角色自动标注) 与 9(数据基础) 均完成
        stage6_status = self.db_manager.get_stage_status(self.video_id, 6)
        if not stage6_status or stage6_status.get("status") != "completed":
            return False, "阶段6（角色自动标注）尚未完成"

        stage9_status = self.db_manager.get_stage_status(self.video_id, 9)
        if not stage9_status or stage9_status.get("status") != "completed":
            return False, "阶段9（数据基础构建）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """根据强制级别，调度角色档案的生成或精炼。"""
        existing_dossier = self.db_manager.get_stage_output(self.video_id, self.stage_number, "character_dossier")

        if force_level == "full":
            self.logger.info("强制模式 'full': 清理旧的角色档案，将从头开始生成。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "character_dossier")
            return self._generate_dossier_from_scratch()

        if force_level == "soft":
            self.logger.info("--- 强制软执行模式 ---")
            if existing_dossier:
                self.logger.info("发现已存在的角色档案，将进行补全和优化。")
                return self._refine_existing_dossier(existing_dossier)
            else:
                self.logger.info("未发现已存在的角色档案，将从头开始生成。")
                return self._generate_dossier_from_scratch()

        if existing_dossier:
            self.logger.info("已存在角色档案，跳过。")
            return True

        # 默认行为：如果不存在，则从头开始生成
        return self._generate_dossier_from_scratch()

    def _get_character_profiles_text(self) -> Optional[str]:
        """获取并格式化作为基准真相的角色设定文本，包含关系。"""
        creative_brief = self.db_manager.get_stage_output(self.video_id, 9, "creative_brief") or {}
        characters = creative_brief.get("characters", [])
        if not characters:
            self.logger.error("在阶段9的输出中缺少角色设定，无法继续。")
            return None

        prompt_parts = []
        for char in characters:
            char_info = [
                f"角色ID: {char.get('character_id', 'N/A')}",
                f"名字: {char.get('name', 'N/A')}",
                f"动机: {char.get('motivations', 'N/A')}",
                f"简介: {char.get('description', 'N/A')}",
            ]
            
            relationships = char.get("relationships", [])
            if relationships:
                rel_texts = []
                for rel in relationships:
                    rel_texts.append(f"  - 与 {rel.get('character_id', '?')} ({rel.get('relationship_type', '未知')})")
                char_info.append("关系:\n" + "\n".join(rel_texts))

            prompt_parts.append("\n".join(char_info))
            
        return "\n\n".join(prompt_parts)

    def _generate_dossier_from_scratch(self) -> bool:
        """从头开始生成角色档案。"""
        self.update_progress("从头开始生成角色档案...")
        character_profiles_text = self._get_character_profiles_text()
        if not character_profiles_text:
            return False
            
        research_summary = self.db_manager.get_all_research_summaries(self.video_id)

        prompt = f"""
# Role
你是一位资深的编剧和剧本医生。

# Task
基于以下的研究资料和角色设定，为**每一个角色**生成一份详尽的角色档案（约500字）。档案应深入挖掘角色的背景、内心冲突和人物弧光。

# Input: Research Summary
---
{research_summary}
---

# Input: Character Profiles
---
{character_profiles_text}
---

# Output Format
请严格按照以下字段输出每个角色的档案，并确保内容详实、深刻：
输出字段：character_id, name, background, motivation, conflict, arc
"""

        try:
            dossier_text = self.client.call_ai(prompt, temperature=0.4).strip()
            self.db_manager.save_stage_output(
                self.video_id, self.stage_number, self.stage_name, "character_dossier", {"text": dossier_text}
            )
            self.logger.info("✅ 角色档案生成成功。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _refine_existing_dossier(self, existing_dossier: Dict[str, Any]) -> bool:
        """在现有档案的基础上进行补全和优化。"""
        self.update_progress("正在优化和补全现有角色档案...")
        character_profiles_text = self._get_character_profiles_text()
        if not character_profiles_text:
            return False

        existing_dossier_text = existing_dossier.get("text", "")
        if not existing_dossier_text:
            self.logger.warning("现有档案内容为空，将回退到从头开始生成。")
            return self._generate_dossier_from_scratch()
            
        research_summary = self.db_manager.get_all_research_summaries(self.video_id)

        prompt = CHARACTER_DOSSIER_REFINE_PROMPT.format(
            research_summary=research_summary,
            character_profiles_text=character_profiles_text,
            existing_dossier_text=existing_dossier_text,
        )

        try:
            refined_dossier_text = self.client.call_ai(prompt, temperature=0.5).strip()
            self.db_manager.save_stage_output(
                self.video_id, self.stage_number, self.stage_name, "character_dossier", {"text": refined_dossier_text}
            )
            self.logger.info("✅ 角色档案优化补全成功。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False
