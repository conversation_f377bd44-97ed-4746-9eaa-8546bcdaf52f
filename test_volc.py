from openai import OpenAI

from pydantic import BaseModel

client = OpenAI(
    # 从环境变量中获取方舟 API Key
    api_key="911beb8d-12a8-403b-993a-7fd2d4dd2bd0",
    base_url="https://ark.cn-beijing.volces.com/api/v3",
)


class Step(BaseModel):
    explanation: str
    output: str


class MathResponse(BaseModel):
    steps: list[Step]
    final_answer: str


completion = client.beta.chat.completions.parse(
    model="doubao-seed-1-6-250615",  # 替换为您需要使用的模型
    messages=[
        {"role": "system", "content": "你是一位数学辅导老师。"},
        {"role": "user", "content": "使用中文解题: 8x + 9 = 32 and x + y = 1"},
    ],
    response_format=MathResponse,
    extra_body={
        "thinking": {
            "type": "disabled"  # 不使用深度思考能力
            # "type": "enabled" # 使用深度思考能力
        }
    },
)
resp = completion.choices[0].message.parsed
if resp is not None:
    # 打印 JSON 格式结果
    print(resp.model_dump_json(indent=2))
