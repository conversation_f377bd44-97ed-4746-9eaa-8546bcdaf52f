"""
AI JSON输出的Pydantic模型定义
用于强制AI返回结构化的、可预测的、经过验证的对象。
"""

from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field, model_validator


# --- 微观场景分析 ---
class ShotAnalysisModel(BaseModel):
    shot_type: str = Field(..., description="景别 (从 特写, 近景, 中景, 全景, 远景 中选择)。")
    camera_angle: str = Field(..., description="机位角度 (从 平视, 俯视, 仰视 中选择)。")
    camera_movement: str = Field(..., description="运镜方式 (从 固定镜头, 推, 拉, 摇, 移, 跟, 升降 中选择)。")
    composition: str = Field(..., description="构图特点 (例如：三分法, 对称构图等)。")
    lighting: str = Field(..., description="光线风格 (例如：高调, 低调, 自然光等)。")


class SceneAnalysisResponse(BaseModel):
    people: str = Field(..., description="识别画面中的主要人物，并简要描述他们的状态或外貌。")
    setting: str = Field(..., description="详细描述场景环境、地点和时间。")
    main_action: str = Field(..., description="描述该镜头内的核心动作或事件。")
    visual_description: str = Field(..., description="对整个镜头画面的综合性、叙事性描述，总结核心视觉元素和氛围。")
    on_screen_text: str = Field(
        ..., description="识别并转录画面中出现的任何重要文字，如标题、路牌、屏幕上的消息等。如果没有则返回空字符串。"
    )
    emotion: str = Field(
        ...,
        description="分析人物的情绪，特别是肢体语言和面部表情所传达的。对于文艺片，请深入解读复杂情感；对于动作片，请关注战斗情绪。",
    )
    key_objects: str = Field(..., description="列出与核心动作或叙事/象征意义相关的关键物体。")
    shot_analysis: ShotAnalysisModel = Field(..., description="包含详细镜头语言分析的对象。")
    # Genre-specific fields
    action_intensity: Optional[str] = Field(
        None, description="[仅动作片] 评估当前镜头的动作激烈程度 (从 低, 中, 高 中选择)。"
    )
    subtext: Optional[str] = Field(None, description="[仅剧情片] 基于画面和对话，推断角色未说出口的想法或感受。")
    comedy_type: Optional[str] = Field(
        None, description="[仅喜剧片] 识别并分类笑点类型 (例如：反差萌, 夸张, 意外, 肢体喜剧等)。"
    )


# --- 镜头到场景聚合 ---
class SceneGroupingItem(BaseModel):
    start_shot_id: int = Field(..., description="组成这个场景的第一个镜头的ID。")
    end_shot_id: int = Field(..., description="组成这个场景的最后一个镜头的ID。")
    summary: str = Field(..., description="对这个场景的整体内容进行简洁的叙事性总结。")
    narrative_purpose: str = Field(..., description="总结这个场景在故事中的作用。")


class SceneGroupingResponse(BaseModel):
    scenes: List[SceneGroupingItem]


# --- 场景到序列分析 ---
class SequenceAnalysisItem(BaseModel):
    scene_numbers: List[int] = Field(..., description="组成这个序列的所有原始场景编号。")
    theme: str = Field(..., description="这个序列的核心主题或中心思想。")
    summary: str = Field(..., description="对这个序列的完整情节进行连贯的叙事性总结。")
    emotional_arc: str = Field(..., description="描述这个序列从开始到结束的情绪变化曲线。")


class SequenceAnalysisResponse(BaseModel):
    sequences: List[SequenceAnalysisItem]


# --- 新增：角色情感弧线分析模型 ---
class CharacterEmotionalArc(BaseModel):
    character_name: str = Field(..., description="角色的名称。")
    emotional_arc: str = Field(..., description="该角色在此序列中的情感变化或心路历程的详细描述。")


class CharacterEmotionalArcResponse(BaseModel):
    character_arcs: List[CharacterEmotionalArc]


# --- D2S Rewriter: 故事大纲模型 ---
class StoryOutlineItem(BaseModel):
    scene_number: int = Field(..., description="场景编号，从1开始。")
    scene_goal: str = Field(..., description="该场景在故事中的核心目标或作用。")
    characters_present: List[str] = Field(..., description="在此场景中出现的角色的character_id列表。")
    setting: str = Field(..., description="场景发生的地点和时间。")
    candidate_clip_ids: List[str] = Field(..., description="可用于视觉化呈现该场景的一组候选clip_id列表。")
    summary: str = Field(
        ...,
        description="对该场景内容的详细段落描述。请详细阐述场景的起因、经过、结果，以及关键角色的行动和反应，确保每个场景的描述都足够丰富，能够独立成段。",
    )


class StoryOutlineResponse(BaseModel):
    story_outline: List[StoryOutlineItem]


# --- 新增：剧本创作策略模型 ---
class SceneWritingStrategy(BaseModel):
    scene_number: int = Field(..., description="对应的场景编号。")
    narrative_focus: str = Field(
        ..., description="该场景的核心叙事焦点（例如：'展示安娜的脆弱', '营造紧张的追逐氛围'）。"
    )
    pacing: str = Field(..., description="该场景的建议节奏 (例如: '快节奏', '慢节奏', '紧张', '抒情')。")
    narration_style: str = Field(
        ..., description="该场景的旁白策略 (例如: '纯视觉叙C事，无旁白', '客观解说背景', '第一人称内心独白')。"
    )


class ScriptWritingStrategyResponse(BaseModel):
    global_narration_tone: str = Field(..., description="整部影片的全局旁白基调和风格。")
    hook_strategy: Optional[str] = Field(None, description="视频开头的“钩子”策略，用于迅速抓住观众注意力。")
    climax_strategy: Optional[str] = Field(None, description="视频高潮部分的核心论点或关键转折的揭示策略。")
    conclusion_strategy: Optional[str] = Field(None, description="视频结尾的总结与主题升华策略。")
    scene_strategies: List[SceneWritingStrategy] = Field(
        ..., description="针对每个场景的具体创作策略列表，主要构成故事的“主体”。"
    )


# --- 电影解说文案 ---
class NarrationType(str, Enum):
    NARRATOR = "NARRATOR"
    INNER_MONOLOGUE = "INNER_MONOLOGUE"
    CHARACTER_DIALOGUE = "CHARACTER_DIALOGUE"


class ScriptBeat(BaseModel):
    beat_number: int = Field(..., description="节拍编号，在一个场景内从1开始连续编号。")
    source_scene_number: int = Field(..., description="生成此节拍的源场景编号。")
    visual_description: str = Field(..., description="对这个节拍中画面的详细描述，这将用于指导B-Roll的选择。")
    audio_content: Optional[str] = Field(
        None, description="此节拍对应的音频内容。可以是旁白、内心独白或角色对白。如果留空，则表示这是一个纯视觉片段。"
    )
    narration_type: Optional[NarrationType] = Field(
        None,
        description="【仅当 audio_content 存在时有效】明确音频的性质。'NARRATOR' (客观旁白), 'INNER_MONOLOGUE' (内心独白), 'CHARACTER_DIALOGUE' (角色对白)。",
    )

    @model_validator(mode="after")
    def check_audio_logic(self) -> "ScriptBeat":
        if self.audio_content and self.narration_type is None:
            raise ValueError("如果提供了 'audio_content', 则必须指定 'narration_type'。")
        if self.narration_type and self.audio_content is None:
            raise ValueError("如果指定了 'narration_type', 则必须提供 'audio_content'。")
        return self


class MovieCommentaryScriptResponse(BaseModel):
    script: List[ScriptBeat] = Field(..., description="构成整个视频的音画节拍列表。")


# --- 音画映射 ---
# SentenceToSceneMapperResponse 不再需要，但为了兼容性保留


# --- 研究计划 ---
class ResearchPlanResponse(BaseModel):
    core_analysis: List[str]
    background_and_worldview: List[str]
    creators_research: List[str]
    reception_and_legacy: List[str]


# --- 音频分析 ---
class AudioAnalysisResponse(BaseModel):
    transcript: str = Field(..., description="将所有可识别的对话转录为文字。")
    speaker_tone: str = Field(..., description="描述说话者的语气和情感。")
    music_analysis: str = Field(..., description="分析背景音乐的风格和营造的氛围。")
    key_sound_events: List[str] = Field(..., description="列出除对话和音乐外的关键声音事件。")


# 新增：孤儿镜头修复决策模型
class OrphanFixDecisionEnum(str, Enum):
    PRECEDING = "preceding"
    SUCCEEDING = "succeeding"
    NEW_SCENE = "new_scene"


# --- 【核心改动】更新孤儿镜头修复决策模型，使用枚举代替pattern ---
class OrphanFixDecision(BaseModel):
    decision: OrphanFixDecisionEnum = Field(
        ...,
        description="你的决策。必须是 'preceding', 'succeeding', 或 'new_scene' 中的一个。",
    )


# --- D2S: 设计文档解析模型 ---
class RelationshipInfo(BaseModel):
    character_id: str = Field(..., description="关联角色的ID。", alias="target_character_id")
    relationship_type: str = Field(..., description="关系类型，例如 '盟友', '敌人', '导师'。", alias="nature")


class CharacterInfo(BaseModel):
    character_id: str = Field(..., description="角色的唯一标识符，例如 'char_anna'。")
    name: str = Field(..., description="角色的名字。")
    description: str = Field(..., description="角色的外貌、性格和背景故事的详细描述。")
    motivations: str = Field(..., description="驱动角色行动的核心欲望或目标。", alias="motivation")
    relationships: List[RelationshipInfo] = Field(..., description="描述该角色与其他角色的关系。")


class ProjectInfo(BaseModel):
    title: str = Field(..., description="项目的正式标题。", alias="project_title")
    logline: str = Field(
        ...,
        description="用一个包含五句话的段落来概括整个故事。第一句介绍背景和主角，接下来三句描述三个核心的冲突或转折点，最后一句总结结局。",
    )
    themes: List[str] = Field(
        ..., description="故事探讨的核心主题，例如 ['忠诚', '牺牲', '成长']。", alias="core_themes"
    )
    target_audience: str = Field(..., description="描述视频的目标观众群体及其特征。")
    narrative_goals: List[str] = Field(..., description="故事旨在传达的关键信息或和实现的情感效果。")


class DesignDocParseResponse(BaseModel):
    project_info: ProjectInfo
    characters: List[CharacterInfo]

    @model_validator(mode="before")
    def restructure_input(cls, data: Any) -> Any:
        if isinstance(data, dict):
            # --- 步骤 1: 重构 project_info ---
            project_overview = data.get("project_overview")
            if "project_info" not in data and project_overview:
                # 初始化一个临时的 project_info 字典
                temp_project_info = {}

                # 从 project_overview 的顶层提取信息
                temp_project_info["target_audience"] = project_overview.get("target_audience")
                temp_project_info["narrative_goals"] = [project_overview.get("core_purpose")]

                # 从内嵌的 original_film_info 提取信息
                original_info = project_overview.get("original_film_info", {})
                temp_project_info["title"] = original_info.get("title")
                temp_project_info["logline"] = original_info.get("core_plot")

                # 从顶层的 core_themes 提取信息
                temp_project_info["themes"] = data.get("core_themes")

                # 将重构好的字典赋给 project_info
                data["project_info"] = {k: v for k, v in temp_project_info.items() if v is not None}

            # --- 步骤 2: 重构 characters ---
            # 同时检查 "character_settings" 和 "character_setup"
            character_settings = data.get("character_settings") or data.get("character_setup")
            if "characters" not in data and character_settings:
                data["characters"] = character_settings

            # --- 步骤 3: 【核心改动】重构并注入 relationships ---
            character_relationships = data.get("character_relationships")
            if character_relationships and data.get("characters"):
                # 创建一个从 character_id 到角色信息字典的映射，以便快速查找
                char_map = {char.get("character_id"): char for char in data["characters"]}

                for rel in character_relationships:
                    source_id = rel.get("source_id")
                    target_id = rel.get("target_id")

                    # 找到源角色
                    if source_id in char_map:
                        source_char = char_map[source_id]
                        # 如果源角色还没有 relationships 列表，则创建一个
                        if "relationships" not in source_char:
                            source_char["relationships"] = []

                        # 构建符合我们模型的关系对象
                        new_relationship = {
                            "target_character_id": target_id,
                            "nature": rel.get("type"),  # 使用别名 'nature' 来匹配 'relationship_type'
                            # 'description' 字段在我们的模型中不存在，所以忽略它
                        }
                        source_char["relationships"].append(new_relationship)
        return data


# --- D2S Reader: 事件识别模型 ---
class NarrativeEvent(BaseModel):
    event_id: str = Field(..., description="为事件生成的唯一ID，格式为 'event_XXX'。")
    event_description: str = Field(..., description="对这个叙事事件的简洁、高度概括的描述。")
    characters_present: List[str] = Field(..., description="在此事件中出现的角色的名字列表。")
    source_clip_ids: List[str] = Field(..., description="构成这个事件的一个或多个视频片段的clip_id列表。")


class EventIdentificationResponse(BaseModel):
    narrative_events: List[NarrativeEvent]


# --- D2S Reader: 因果链接推断模型 ---
class CausalLink(BaseModel):
    source_event_id: str = Field(..., description="因果关系中的源头事件ID。")
    target_event_id: str = Field(..., description="因果关系中的目标事件ID。")
    causality_description: str = Field(..., description="对这条因果关系的简要描述和推理依据。")


class CausalLinkInferenceResponse(BaseModel):
    causal_links: List[CausalLink]


# --- D2S: 剧本评估模型 (REACT-S框架) ---
class ScriptEvaluationScore(BaseModel):
    score: int = Field(..., ge=1, le=5, description="评分 (1-5分)。")
    justification: str = Field(..., description="评分的详细理由。")


class ScriptEvaluationResponse(BaseModel):
    relevance: ScriptEvaluationScore = Field(
        ..., description="关联性 (Relevance): 剧本在多大程度上回应和实现了项目目标和主题？"
    )
    engagement: ScriptEvaluationScore = Field(
        ..., description="吸引力 (Engagement): 剧本是否拥有引人入胜的节奏、生动的对白和坚实的结构？"
    )
    adherence: ScriptEvaluationScore = Field(
        ..., description="遵循度 (Adherence): 角色行为和对白是否严格遵循了角色设定和动机？"
    )
    coherence: ScriptEvaluationScore = Field(
        ..., description="连贯性 (Coherence): 故事的情节发展是否符合逻辑？因果链条在剧本中是否得到了可信的呈现？"
    )
    technical_quality: ScriptEvaluationScore = Field(
        ...,
        description="技术性 (Technical-quality): 剧本格式是否规范？动作和声音描述是否清晰，足以指导后续的AI剪辑决策？",
    )


# --- 新增：镜头序列选择模型 ---
class ShotSequenceSelectionResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(..., description="AI选择的最佳镜头序列的顺序ID列表。")
    justification: str = Field(..., description="AI选择这个序列的理由。")


# --- 新增：标语生成模型 ---
class TaglineResponse(BaseModel):
    tagline: str = Field(..., description="为视频生成的一句简洁、有力、吸引人的宣传标语。")


# --- 新增：D2S 粗剪精炼响应模型 ---
class RoughCutRefinementResponse(BaseModel):
    selected_shot_order_ids: List[int] = Field(
        ..., description="AI剪辑师最终决定使用的、按时间顺序排列的镜头 order_id 列表。"
    )
    justification: str = Field(..., description="AI做出这个剪辑选择的简要理由。")


# --- 新增：D2S 角色补全响应模型 ---
class CharacterCompletionResponse(BaseModel):
    completed_characters: List[CharacterInfo] = Field(..., description="一个包含新补充的、完整的角色信息对象的列表。")


# --- 新增：D2S 单场景剧本评估响应模型 ---
class SceneScriptEvaluationResponse(BaseModel):
    score: int = Field(..., ge=1, le=5, description="对该场景剧本的总体评分 (1-5分)。")
    is_ready_for_production: bool = Field(..., description="判断该稿件是否已达到可用于生产的质量标准。")
    justification: str = Field(..., description="评分的简要理由。")
    suggested_improvements: str = Field(..., description="具体的、可操作的修改建议，用于指导下一轮修订。")
