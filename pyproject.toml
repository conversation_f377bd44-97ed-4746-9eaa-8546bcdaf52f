[project]
name = "telegram-monitor"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiohttp>=3.12.12",
    "apscheduler>=3.11.0",
    "cryptg>=0.5.0.post0",
    "hachoir>=3.3.0",
    "pillow>=11.2.1",
    "python-dotenv>=1.1.0",
    "python-telegram-bot>=22.1",
    "telethon>=1.40.0",
    "pysocks>=1.7.1",
    "openai>=1.0.0", # <-- 新增此行
    "boto3>=1.38.35",
]


[tool.ruff]
line-length = 120

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
