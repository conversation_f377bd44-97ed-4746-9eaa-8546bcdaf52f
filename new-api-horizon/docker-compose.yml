services:
  uniapi:
    container_name: uniapi
    image: yym68686/uni-api:latest
    volumes:
      - ./uniapi.yaml:/home/<USER>
      - ./uniapi-data:/home/<USER>
    labels:
      - traefik.enable=true
      - traefik.http.routers.uniapi.rule=Host(`uniapi.pepeai.site`)
      - traefik.http.routers.uniapi.entrypoints=websecure
      - traefik.http.routers.uniapi.tls=true
      - traefik.http.routers.uniapi.tls.certresolver=le
      # # 应用多个中间件，包括请求体限制、无缓冲和流式响应头
      # - "traefik.http.routers.uniapi.middlewares=limit-request-body@docker,no-buffer@docker,stream-headers@docker"
      # 设置路由规则语法为v2以保持兼容性
      - "traefik.http.routers.uniapi.ruleSyntax=v2"
      # # 请求体限制中间件
      # - "traefik.http.middlewares.limit-request-body.buffering.maxRequestBodyBytes=50000000"
      # - "traefik.http.middlewares.limit-request-body.buffering.memRequestBodyBytes=50000000"
      # # 无缓冲中间件
      # - "traefik.http.middlewares.no-buffer.buffering.maxResponseBodyBytes=0"
      # - "traefik.http.middlewares.no-buffer.buffering.memResponseBodyBytes=0"
      # - "traefik.http.middlewares.no-buffer.buffering.retryExpression="
      # # 流式响应头中间件
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.Cache-Control=no-cache"
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.Connection=keep-alive"
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.X-Accel-Buffering=no"
      # 响应转发设置
      - "traefik.http.services.uniapi.loadbalancer.responseForwarding.flushInterval=1ms"
    networks:
      - traefik_network

  newapi:
    container_name: newapi
    image: calciumion/new-api-horizon:latest
    volumes:
      - ./newapi-data:/data
    labels:
      - traefik.enable=true
      - traefik.http.routers.newapi.rule=Host(`newapi.pepeai.site`)
      - traefik.http.routers.newapi.entrypoints=websecure
      - traefik.http.routers.newapi.tls=true
      - traefik.http.routers.newapi.tls.certresolver=le
      # 应用多个中间件，包括请求体限制、无缓冲和流式响应头
      # - "traefik.http.routers.newapi.middlewares=limit-request-body@docker,no-buffer@docker,stream-headers@docker"
      # 设置路由规则语法为v2以保持兼容性
      - "traefik.http.routers.newapi.ruleSyntax=v2"
      # # 请求体限制中间件
      # - "traefik.http.middlewares.limit-request-body.buffering.maxRequestBodyBytes=50000000"
      # - "traefik.http.middlewares.limit-request-body.buffering.memRequestBodyBytes=50000000"
      # # 无缓冲中间件
      # - "traefik.http.middlewares.no-buffer.buffering.maxResponseBodyBytes=0"
      # - "traefik.http.middlewares.no-buffer.buffering.memResponseBodyBytes=0"
      # - "traefik.http.middlewares.no-buffer.buffering.retryExpression="
      # # 流式响应头中间件
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.Cache-Control=no-cache"
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.Connection=keep-alive"
      # - "traefik.http.middlewares.stream-headers.headers.customResponseHeaders.X-Accel-Buffering=no"
      # 响应转发设置
      - "traefik.http.services.newapi.loadbalancer.responseForwarding.flushInterval=1ms"
    networks:
      - traefik_network

  mcps:
    container_name: mcps
    image: ghcr.io/ptbsare/mcp-proxy-server/mcp-proxy-server:latest-bundled-mcpservers-playwright
    restart: unless-stopped
    labels:
      - traefik.enable=true
      - traefik.http.routers.mcps.rule=Host(`mcp.pepeai.site`)
      - traefik.http.routers.mcps.entrypoints=websecure
      - traefik.http.routers.mcps.tls=true
      - traefik.http.routers.mcps.tls.certresolver=le
      - "traefik.http.routers.mcps.ruleSyntax=v2"
      - "traefik.http.services.mcps.loadbalancer.responseForwarding.flushInterval=1ms"
    environment:
      # - ALLOWED_TOKENS=sk-azUpok6AgAhDbbLDOtsLGloFTPRQuiIJphkONTspdyYBLNpv
      - ENABLE_ADMIN_UI=true
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=gjGF46sgXsHvmXdBqmBWUV0EFt8zZILG
    volumes:
      - ./mcps-config:/mcp-proxy-server/config
      - ./mcps-tools:/tools
    networks:
      - traefik_network

  checkmate:
    container_name: checkmate
    image: ghcr.io/bluewave-labs/checkmate-backend-mono:latest
    restart: always
    environment:
      - UPTIME_APP_API_BASE_URL=https://mate.pepeai.site/api/v1
      - UPTIME_APP_CLIENT_HOST=https://mate.pepeai.site
      - DB_CONNECTION_STRING=mongodb://mongodb:27017/uptime_db?replicaSet=rs0
      - REDIS_URL=redis://redis:6379
      - CLIENT_HOST=https://mate.pepeai.site
      - JWT_SECRET=rXK7DTf5aujdUayQstjdt19OtEmVsScyGGifYOf3Pv6uB7lJhG2r9rAQQgfLTKP7
      - SYSTEM_EMAIL_HOST=email-smtp.us-west-2.amazonaws.com
      - SYSTEM_EMAIL_PORT=465
      - SYSTEM_EMAIL_USER=AKIAXG3ZTC2S4KCJ5A4N
      - SYSTEM_EMAIL_PASSWORD=BHdQ7yZPwohS6+DBXwI8CrCmSZ66SqpPE0EyHLHU+D7X
      - SYSTEM_EMAIL_ADDRESS=<EMAIL>
    labels:
      - traefik.enable=true
      - traefik.http.routers.checkmate.rule=Host(`mate.pepeai.site`)
      - traefik.http.routers.checkmate.entrypoints=websecure
      - traefik.http.routers.checkmate.tls=true
      - traefik.http.routers.checkmate.tls.certresolver=le
      # 设置路由规则语法为v2以保持兼容性
      - "traefik.http.routers.checkmate.ruleSyntax=v2"
    depends_on:
      - redis
      - mongodb
    networks:
      - traefik_network

  redis:
    container_name: redis
    image: ghcr.io/bluewave-labs/checkmate-redis:latest
    restart: always
    volumes:
      - ./redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 5s
    networks:
      - traefik_network

  mongodb:
    container_name: mongodb
    image: ghcr.io/bluewave-labs/checkmate-mongo:latest
    restart: always
    command: ["mongod", "--quiet", "--replSet", "rs0", "--bind_ip_all"]
    volumes:
      - ./mongo-data:/data/db
    healthcheck:
      test: echo "try { rs.status() } catch (err) { rs.initiate({_id:'rs0',members:[{_id:0,host:'mongodb:27017'}]}) }" | mongosh --port 27017 --quiet
      interval: 5s
      timeout: 30s
      start_period: 0s
      start_interval: 1s
      retries: 30
    networks:
      - traefik_network

  traefik:
    image: traefik:v3.4
    container_name: traefik
    command:
      # Core configuration for v3
      - "--core.defaultRuleSyntax=v2"

      # Global configuration
      - "--api.insecure=true" # Enable API/Dashboard (insecure, for debugging only)
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=traefik_network"

      # Entrypoints
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"

      # HTTP to HTTPS redirect
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"

      # Let's Encrypt configuration
      - "--certificatesresolvers.le.acme.email=${CERTBOT_EMAIL:-<EMAIL>}"
      - "--certificatesresolvers.le.acme.storage=/letsencrypt/acme.json"
      - "--certificatesresolvers.le.acme.httpchallenge=true"
      - "--certificatesresolvers.le.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.le.acme.caserver=https://acme-v02.api.letsencrypt.org/directory"

      # Logging and Observability
      # - "--log.level=DEBUG"
      - "--accesslog=true"
      - "--accesslog.addInternals=true"
      # - "--metrics.prometheus=true"
      # - "--tracing=true"

      # 添加响应转发配置
      - "--serversTransport.forwardingTimeouts.responseHeaderTimeout=0"
      - "--serversTransport.forwardingTimeouts.idleConnTimeout=300s"
    ports:
      - "80:80" # 映射 HTTP 端口
      - "443:443" # 映射 HTTPS 端口
      - "127.0.0.1:8080:8080" # 映射 Traefik Dashboard 端口 (如果启用 api.insecure=true)
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./letsencrypt:/letsencrypt"
    networks:
      - traefik_network # Traefik 需要连接到应用所在的网络
    restart: unless-stopped

networks:
  traefik_network:
    driver: bridge
    name: traefik_network

volumes:
  vhost:
  html:
  acme:

  # svg2img:
  #   container_name: svg2img
  #   build: ../dify-svg2img
  #   environment:
  #     - VIRTUAL_HOST=svg2img.pepeai.site
  #     - LETSENCRYPT_HOST=svg2img.pepeai.site
  #     - VIRTUAL_PORT=8000
  #   networks:
  #     - traefik_network

  # flowise:
  #   container_name: flowise
  #   image: flowiseai/flowise
  #   restart: always
  #   environment:
  #     - VIRTUAL_HOST=flowise.pepeai.site
  #     - LETSENCRYPT_HOST=flowise.pepeai.site
  #     - VIRTUAL_PORT=3000
  #   volumes:
  #     - ./flowise-data:/root/.flowise
  #   entrypoint: /bin/sh -c "sleep 3; flowise start --FLOWISE_USERNAME=$FLOWISE_USERNAME --FLOWISE_PASSWORD=$FLOWISE_PASSWORD"
  #   networks:
  #     - traefik_network

  # voapi:
  #   container_name: voapi
  #   image: voapi/voapi:latest
  #   restart: always
  #   command: --log-dir /app/logs
  #   volumes:
  #     - ./voapi-data:/data
  #     - ./voapi-logs:/app/logs
  #   # extra_hosts:
  #   #   - "host.docker.internal:host-gateway"
  #   environment:
  #     # - SQL_DSN=root:123456@tcp(host.docker.internal:3306)/voapi?charset=utf8mb4&parseTime=True&loc=Local  # 修改此行，或注释掉以使用 SQLite 作为数据库
  #     - REDIS_CONN_STRING=redis://apiredis
  #     - SESSION_SECRET=jdkslfweuoywoeiio2323jhkweh  # 启动前必须手动修改此值为随机字符串
  #     - TZ=Asia/Shanghai
  #     - VIRTUAL_HOST=voapi.pepeai.site
  #     - LETSENCRYPT_HOST=voapi.pepeai.site
  #     - VIRTUAL_PORT=3000
  #   networks:
  #     - traefik_network

  #   depends_on:
  #     - apiredis
  #   healthcheck:
  #     test: [ "CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $2}'" ]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # apiredis:
  #   image: redis:latest
  #   container_name: apiredis
  #   restart: always
  #   networks:
  #     - traefik_network
