# gate-xstocks

一个用于监控 Solana 链上 xStocks 代币与 Gate.io 交易所价格差异的 Python 应用程序。当价差超过预设阈值时，通过 AWS SES 发送邮件通知。

## 设置

1.  **克隆仓库：**
    ```bash
    git clone https://github.com/your-username/gate-xstocks.git
    cd gate-xstocks
    ```

2.  **创建虚拟环境并安装依赖：：**
    ```bash
    python -m venv .venv
    source .venv/bin/activate  # 在 Windows 上使用 `.venv\Scripts\activate`
    uv pip install -e .
    ```
    *注意：本项目使用 `uv` 进行依赖管理。*

3.  **配置环境变量：**
    在项目根目录创建 `.env` 文件，参考 `.env.example` 并填入您的凭据：
    ```
    # Gate.io API 凭据
    GATE_API_KEY=your_gate_api_key
    GATE_API_SECRET=your_gate_api_secret

    # AWS SES 邮件服务凭据
    AWS_ACCESS_KEY_ID=your_aws_access_key_id
    AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
    AWS_REGION=your_aws_region
    SENDER_EMAIL=<EMAIL>
    RECIPIENT_EMAIL=<EMAIL>
    ```

4.  **（可选）自定义代币列表：**
    您可以编辑 `config.py` 文件来添加、删除或修改要监控的代币及其 Solana 合约地址。

## 使用

运行主应用程序：
```bash
python main.py
```
