{"tasks": [{"name": "history", "categories": ["combination"]}, {"name": "expect_act_timeout", "categories": ["regression"]}, {"name": "extract_repo_name", "categories": ["extract"]}, {"name": "amazon_add_to_cart", "categories": ["act"]}, {"name": "instructions", "categories": ["regression", "combination"]}, {"name": "bidnet", "categories": ["act"]}, {"name": "ionwave", "categories": ["act", "regression"]}, {"name": "nonsense_action", "categories": ["act"]}, {"name": "peeler_simple", "categories": ["act"]}, {"name": "simple_google_search", "categories": ["act"]}, {"name": "vantechjournal", "categories": ["act"]}, {"name": "wikipedia", "categories": ["act"]}, {"name": "allrecipes", "categories": ["combination"]}, {"name": "arxiv", "categories": ["combination"]}, {"name": "extract_collaborators", "categories": ["combination"]}, {"name": "extract_github_commits", "categories": ["combination"]}, {"name": "imdb_movie_details", "categories": ["combination"]}, {"name": "peeler_complex", "categories": ["combination"]}, {"name": "sciquest", "categories": ["combination"]}, {"name": "wichita", "categories": ["combination", "regression"]}, {"name": "hn_aisdk", "categories": ["llm_clients"]}, {"name": "hn_langchain", "categories": ["llm_clients"]}, {"name": "hn_customOpenAI", "categories": ["llm_clients"]}, {"name": "apple", "categories": ["experimental"]}, {"name": "combination_sauce", "categories": ["experimental"]}, {"name": "costar", "categories": ["experimental"]}, {"name": "extract_aigrant_companies", "categories": ["regression"]}, {"name": "extract_capacitor_info", "categories": ["experimental"]}, {"name": "extract_partners", "categories": ["experimental"]}, {"name": "extract_press_releases", "categories": ["experimental"]}, {"name": "extract_snowshoeing_destinations", "categories": ["experimental"]}, {"name": "homedepot", "categories": ["experimental"]}, {"name": "rakuten_jp", "categories": ["experimental"]}, {"name": "stock_x", "categories": ["experimental"]}, {"name": "ted_talk", "categories": ["experimental"]}, {"name": "extract_baptist_health", "categories": ["extract"]}, {"name": "extract_github_stars", "categories": ["extract"]}, {"name": "extract_memorial_healthcare", "categories": ["extract", "regression"]}, {"name": "extract_nhl_stats", "categories": ["extract"]}, {"name": "extract_professional_info", "categories": ["extract"]}, {"name": "extract_csa", "categories": ["extract"]}, {"name": "extract_resistor_info", "categories": ["extract"]}, {"name": "extract_rockauto", "categories": ["extract"]}, {"name": "extract_staff_members", "categories": ["extract"]}, {"name": "ionwave_observe", "categories": ["observe"]}, {"name": "panamcs", "categories": ["observe"]}, {"name": "vanta_h", "categories": ["experimental"]}, {"name": "extract_area_codes", "categories": ["extract"]}, {"name": "extract_public_notices", "categories": ["extract"]}, {"name": "extract_jstor_news", "categories": ["extract"]}, {"name": "extract_apartments", "categories": ["extract"]}, {"name": "extract_zillow", "categories": ["extract"]}, {"name": "observe_github", "categories": ["observe", "regression"]}, {"name": "observe_vantechjournal", "categories": ["observe", "regression"]}, {"name": "observe_amazon_add_to_cart", "categories": ["observe"]}, {"name": "observe_simple_google_search", "categories": ["observe"]}, {"name": "observe_yc_startup", "categories": ["observe"]}, {"name": "observe_taxes", "categories": ["observe"]}, {"name": "observe_iframes1", "categories": ["regression", "observe"]}, {"name": "observe_iframes2", "categories": ["regression", "observe"]}, {"name": "extract_hamilton_weather", "categories": ["targeted_extract", "regression"]}, {"name": "extract_regulations_table", "categories": ["targeted_extract"]}, {"name": "extract_recipe", "categories": ["targeted_extract"]}, {"name": "extract_aigrant_targeted", "categories": ["targeted_extract"]}, {"name": "extract_aigrant_targeted_2", "categories": ["targeted_extract"]}, {"name": "extract_geniusee", "categories": ["targeted_extract"]}, {"name": "extract_geniusee_2", "categories": ["targeted_extract"]}, {"name": "scroll_50", "categories": ["regression", "act"]}, {"name": "scroll_75", "categories": ["regression", "act"]}, {"name": "nextChunk", "categories": ["regression", "act"]}, {"name": "prevChunk", "categories": ["regression", "act"]}, {"name": "google_flights", "categories": ["act"]}, {"name": "extract_jfk_links", "categories": ["extract"]}, {"name": "extract_single_link", "categories": ["extract"]}, {"name": "dropdown", "categories": ["act"]}, {"name": "radio_btn", "categories": ["act"]}, {"name": "checkboxes", "categories": ["act"]}, {"name": "agent/iframe_form", "categories": ["agent"]}, {"name": "agent/iframe_form_multiple", "categories": ["agent"]}, {"name": "agent/google_flights", "categories": ["agent"]}, {"name": "agent/sf_library_card", "categories": ["agent"]}, {"name": "agent/sf_library_card_multiple", "categories": ["agent"]}, {"name": "login", "categories": ["act", "regression"]}, {"name": "iframe_hn", "categories": ["extract"]}, {"name": "iframe_same_proc", "categories": ["act"]}, {"name": "iframe_form_filling", "categories": ["act"]}, {"name": "iframes_nested", "categories": ["act"]}, {"name": "no_js_click", "categories": ["act", "regression"]}, {"name": "tab_handling", "categories": ["act"]}, {"name": "agent/kayak", "categories": ["agent"]}, {"name": "multi_tab", "categories": ["act"]}, {"name": "shadow_dom", "categories": ["act"]}, {"name": "os_dropdown", "categories": ["act"]}, {"name": "custom_dropdown", "categories": ["act"]}, {"name": "hidden_input_dropdown", "categories": ["act"]}, {"name": "nested_iframes_2", "categories": ["act"]}]}