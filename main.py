import argparse
import asyncio
import json
import os
from datetime import datetime, timedelta

import openai
import boto3
from botocore.exceptions import ClientError
from aiohttp import web

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from dotenv import load_dotenv
from telethon import Telegram<PERSON>lient, events
from telethon.errors import SessionPasswordNeededError

load_dotenv()

# --- 从 .env 文件加载配置 ---
api_id = os.getenv("API_ID")
api_hash = os.getenv("API_HASH")
phone_number = os.getenv("PHONE_NUMBER")
session_name = os.getenv("SESSION_NAME", "telegram")

# --- 加载AI和邮件配置 ---
openai_api_key = os.getenv("OPENAI_API_KEY")
openai_api_base = os.getenv("OPENAI_API_BASE")
ai_model_name = os.getenv("AI_MODEL_NAME", "deepseek-chat")
recipient_email = os.getenv("RECIPIENT_EMAIL")
sender_email = os.getenv("SENDER_EMAIL")
aws_region = os.getenv("AWS_REGION")
aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")

# 检查必要的凭证是否存在
if not all([api_id, api_hash, phone_number]):
    raise ValueError("请在 .env 文件中设置 API_ID, API_HASH 和 PHONE_NUMBER")
api_id = int(api_id)

# 初始化全局变量，将在main()函数中填充
target_groups = []
messages_dict = {}
is_ai_healthy = True  # AI服务初始状态为健康

# --- 代理配置 (可选) ---
proxy_type = os.getenv("PROXY_TYPE")
proxy_host = os.getenv("PROXY_HOST")
proxy_port = os.getenv("PROXY_PORT")

proxy = None
if proxy_type and proxy_host and proxy_port:
    try:
        proxy = (proxy_type, proxy_host, int(proxy_port))
    except (ValueError, TypeError):
        print("警告：代理端口号无效，将不使用代理。")

# 创建客户端
client = TelegramClient(session_name, api_id, api_hash, proxy=proxy)


async def ensure_login():
    """确保客户端已登录，处理验证码和两步验证密码。"""
    if not await client.is_user_authorized():
        await client.send_code_request(phone_number)
        try:
            await client.sign_in(phone_number, input(f"请输入发送到 {phone_number} 的验证码: "))
        except SessionPasswordNeededError:
            await client.sign_in(phone_number, password=input("您的账户已启用两步验证，请输入密码: "))
        except Exception as e:
            print(f"登录失败: {e}")
            return False
    return True


async def analyze_messages_with_ai(summary_data):
    """使用AI模型分析消息内容，并带有重试机制和健康状态更新。"""
    global is_ai_healthy  # 声明使用全局变量
    if not openai_api_key or not openai_api_base:
        print("警告: AI配置不完整，跳过分析。")
        return None

    ai_client = openai.AsyncOpenAI(api_key=openai_api_key, base_url=openai_api_base)
    content_to_analyze = json.dumps(summary_data, ensure_ascii=False, indent=2)
    system_prompt = """
    你是一个专业的内容分析师。你的任务是分析给定的Telegram聊天记录，并根据其重要性、新颖性或趣味性进行评级。
    请将内容分为以下四类之一：
    1.  '特别牛逼': 包含极其重要、具有颠覆性观点或重大机会的内容。
    2.  '牛逼': 包含很有价值的见解、深度讨论或明确的商业机会。
    3.  '有点意思': 包含有趣、新颖的观点或值得关注的讨论。
    4.  '平平无奇': 常规的聊天内容，没有特别的价值。

    评级案例：
    - 如果内容是"美国总统特朗普宣布发币trump"，这属于首次发生的重大事件，应评为 '特别牛逼'。
    - 如果之后出现类似内容"美国总统特朗普宣布第二次发币melania"，由于新颖性下降，应评为 '有点意思'。
    - 如果内容是关于一个著名的meme币（例如 'aura'），在一年前发行、接近归零后，突然暴涨10倍以上，这代表着重大的市场异动和机会，应评为 '牛逼'。

    请以JSON格式返回你的分析结果，必须包含 'rating' 和 'reason' 两个字段。'reason' 字段请用中文简要说明你做出该评级的原因。
    例如: {"rating": "牛逼", "reason": "讨论中提到了一个新的技术趋势xxx，并有深入的分析。"}
    """
    max_retries = 3
    for attempt in range(max_retries):
        print(f"\n正在调用AI进行内容分析... (尝试 {attempt + 1}/{max_retries})")
        json_str = ""
        try:
            response = await ai_client.chat.completions.create(
                model=ai_model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"请分析以下聊天记录:\n{content_to_analyze}"},
                ],
                response_format={"type": "json_object"},
                temperature=0.2,
            )
            raw_response_content = response.choices[0].message.content
            if not raw_response_content:
                raise ValueError("模型返回了空内容")
            start_index = raw_response_content.find("{")
            end_index = raw_response_content.rfind("}")
            if start_index == -1 or end_index == -1:
                raise ValueError("响应中未找到有效的JSON对象")
            json_str = raw_response_content[start_index : end_index + 1]
            analysis_result = json.loads(json_str)
            print(f"AI分析完成: 评级 - {analysis_result.get('rating')}, 原因 - {analysis_result.get('reason')}")

            is_ai_healthy = True  # 成功后，将状态标记为健康
            return analysis_result

        except json.JSONDecodeError as e:
            print(f"AI分析失败：无法解析返回的JSON。清理后的文本: '{json_str}'. 错误: {e}")
        except Exception as e:
            print(f"AI分析失败: {e}")

        if attempt < max_retries - 1:
            print("将在1秒后重试...")
            await asyncio.sleep(1)  # 修改重试间隔为1秒
        else:
            print("已达到最大重试次数，放弃分析。")
            is_ai_healthy = False  # 连续失败后，将状态标记为不健康

    return None


async def extract_details_with_ai(summary_data):
    """使用AI模型从消息中提取关键信息，并对返回格式进行严格校验。"""
    global is_ai_healthy  # 声明使用全局变量
    if not openai_api_key or not openai_api_base:
        print("警告: AI配置不完整，跳过详细信息提取。")
        return "AI详细分析不可用（配置缺失）。"

    ai_client = openai.AsyncOpenAI(api_key=openai_api_key, base_url=openai_api_base)
    content_to_analyze = json.dumps(summary_data, ensure_ascii=False, indent=2)
    system_prompt = """
    你是一位专业的区块链和加密货币分析师。你的任务是从给定的Telegram聊天记录中，提取关于新项目或重要代币的关键信息。
    请重点关注以下几点：
    - 'token_name': 代币或项目名称。
    - 'contract_address': 合约地址（如果提到）。
    - 'market_cap': 市值（如果提到）。
    - 'concept': 项目概念或主要特点的简要概括。
    - 'reason_for_attention': 为什么这个项目/代币值得关注（例如：名人效应、技术创新、市场异动等）。

    返回格式必须是包含一个 'projects' 键的JSON对象。'projects' 的值应该是一个JSON数组，其中包含所有找到的项目对象。
    如果没有找到任何项目，'projects' 的值必须是一个空数组 `[]`。

    例如，如果找到项目:
    {
      "projects": [
        {
          "token_name": "TRUMP",
          "contract_address": "0x1234...abcd",
          "market_cap": "$100M",
          "concept": "基于特朗普的Meme币",
          "reason_for_attention": "美国前总统特朗普宣布发币，具有极高的名人效应和市场关注度。"
        }
      ]
    }
    如果未找到:
    {
      "projects": []
    }
    """
    max_retries = 3
    for attempt in range(max_retries):
        print(f"\n正在调用AI提取详细信息... (尝试 {attempt + 1}/{max_retries})")
        json_str = ""
        try:
            response = await ai_client.chat.completions.create(
                model=ai_model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"请分析以下聊天记录并提取关键信息:\n{content_to_analyze}"},
                ],
                response_format={"type": "json_object"},
                temperature=0.1,
            )
            raw_response_content = response.choices[0].message.content
            if not raw_response_content:
                raise ValueError("模型返回了空内容")
            start_index = raw_response_content.find("{")
            end_index = raw_response_content.rfind("}")
            if start_index == -1 or end_index == -1:
                raise ValueError("响应中未找到有效的JSON对象")
            json_str = raw_response_content[start_index : end_index + 1]
            details_data = json.loads(json_str)
            if not isinstance(details_data, dict) or "projects" not in details_data:
                raise ValueError("AI响应格式不正确，缺少 'projects' 键。")
            projects_list = details_data["projects"]
            if not isinstance(projects_list, list):
                raise ValueError("AI响应中的 'projects' 键不是一个列表。")

            is_ai_healthy = True  # 成功后，将状态标记为健康

            if not projects_list:
                return "AI未能在消息中提取到明确的项目信息。"

            formatted_details = "AI提取的详细信息："
            for i, item in enumerate(projects_list, 1):
                formatted_details += f"\n\n--- 项目 {i} ---\n"
                formatted_details += f"代币/项目名称: {item.get('token_name', '未提供')}\n"
                formatted_details += f"合约地址: {item.get('contract_address', '未提供')}\n"
                formatted_details += f"市值: {item.get('market_cap', '未提供')}\n"
                formatted_details += f"核心概念: {item.get('concept', '未提供')}\n"
                formatted_details += f"关注原因: {item.get('reason_for_attention', '未提供')}"
            return formatted_details.strip()

        except json.JSONDecodeError as e:
            print(f"AI详细信息提取失败：无法解析返回的JSON。清理后的文本: '{json_str}'. 错误: {e}")
        except Exception as e:
            print(f"AI详细信息提取失败: {e}")

        if attempt < max_retries - 1:
            print("将在1秒后重试...")
            await asyncio.sleep(1)  # 修改重试间隔为1秒

    is_ai_healthy = False  # 连续失败后，将状态标记为不健康
    return "AI详细信息提取失败，请检查服务日志。"


async def send_email_alert(subject, body):
    """使用 AWS SES 发送邮件提醒（在单独的线程中以避免阻塞）。"""
    
    def _send():
        """包含所有阻塞代码的内部同步函数。"""
        if not all([recipient_email, sender_email, aws_region, aws_access_key_id, aws_secret_access_key]):
            print("警告: AWS SES 邮件配置不完整，跳过发送。请检查.env文件。")
            return

        # 为邮件正文创建HTML版本
        html_body = f"""
        <html>
        <head></head>
        <body>
          <p>{body.replace("\n", "<br>")}</p>
        </body>
        </html>
        """
        CHARSET = "UTF-8"

        # 创建 SES 客户端
        ses_client = boto3.client(
            "ses",
            region_name=aws_region,
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
        )

        print(f"正在通过 AWS SES 向 {recipient_email} 发送邮件提醒...")
        try:
            response = ses_client.send_email(
                Destination={"ToAddresses": [recipient_email]},
                Message={
                    "Body": {
                        "Html": {"Charset": CHARSET, "Data": html_body},
                        "Text": {"Charset": CHARSET, "Data": body},
                    },
                    "Subject": {"Charset": CHARSET, "Data": subject},
                },
                Source=sender_email,
            )
            print(f"邮件发送成功! Message ID: {response['MessageId']}")
        except ClientError as e:
            error_message = e.response.get("Error", {}).get("Message", "未知错误")
            print(f"邮件发送失败: {error_message}")
        except Exception as e:
            print(f"邮件发送时发生未知异常: {e}")

    await asyncio.to_thread(_send)


async def list_chats():
    """连接并列出所有对话（群组、频道、用户）及其ID。"""
    print("正在连接到Telegram以获取聊天列表...")
    await client.connect()

    if not await ensure_login():
        await client.disconnect()
        return

    print("\n成功登录。正在获取聊天列表...")
    print("=====================================================================")
    print(f"{'聊天/频道名称':<40} | {'ID'}")
    print("---------------------------------------------------------------------")

    async for dialog in client.iter_dialogs():
        name = dialog.name.replace("\n", " ")  # 防止名称中的换行符破坏格式
        print(f"{name:<40} | {dialog.id}")

    print("=====================================================================")
    print("\n提示: 要监控某个群组或频道，请将其对应的ID添加到您的 .env 文件的 TARGET_GROUPS 变量中。")
    print("对于私有频道或超级群组，ID通常是负数。")

    await client.disconnect()
    print("\n客户端已断开连接。")


# 定义消息处理函数
async def process_collected_messages():
    print(f"\n===== 消息更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} =====")

    # 创建一个包含所有群组消息的汇总数据
    summary = {"timestamp": datetime.now().isoformat(), "groups": {}}

    for group_id, messages in messages_dict.items():
        try:
            group_entity = await client.get_entity(group_id)
            group_name = getattr(group_entity, "title", str(group_id))
            print(f"\n群组: {group_name} (ID: {group_id}), 消息数量: {len(messages)}")

            # 添加到汇总数据
            summary["groups"][group_id] = {"name": group_name, "messages": messages}

            if messages:
                print("最新消息:")
                for i, msg in enumerate(messages, 1):
                    print(f"{i}. {msg['sender']}: {msg['text']}")
            else:
                print("此时间段内无新消息")
        except Exception as e:
            print(f"处理群组 {group_id} 的消息时出错: {e}")

    # 保存汇总数据到文件
    os.makedirs("telegram_logs", exist_ok=True)
    filename = f"telegram_logs/messages_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)

    # --- 新增: 聚合过去30分钟的日志 ---
    log_dir = "telegram_logs"
    thirty_minutes_ago = datetime.now() - timedelta(minutes=30)
    aggregated_summary = {"groups": {}}

    print("\n开始聚合过去30分钟的日志...")
    for log_filename in os.listdir(log_dir):
        if log_filename.startswith("messages_") and log_filename.endswith(".json"):
            try:
                # 从文件名解析时间
                ts_str = log_filename.replace("messages_", "").replace(".json", "")
                log_time = datetime.strptime(ts_str, "%Y%m%d_%H%M%S")

                if log_time >= thirty_minutes_ago:
                    log_path = os.path.join(log_dir, log_filename)
                    print(f"正在聚合文件: {log_filename}")
                    with open(log_path, "r", encoding="utf-8") as log_file:
                        log_data = json.load(log_file)
                        for group_id, group_data in log_data.get("groups", {}).items():
                            # 如果聚合字典中还没有这个群组，则创建它
                            if group_id not in aggregated_summary["groups"]:
                                aggregated_summary["groups"][group_id] = {"name": group_data["name"], "messages": []}
                            # 添加消息
                            aggregated_summary["groups"][group_id]["messages"].extend(group_data["messages"])
            except (ValueError, FileNotFoundError) as e:
                print(f"处理日志文件 {log_filename} 时出错: {e}")
    # ------------------------------------

    # --- AI分析和邮件提醒 ---
    if any(group.get("messages") for group in aggregated_summary["groups"].values()):
        analysis_result = await analyze_messages_with_ai(aggregated_summary)
        if analysis_result:
            rating = analysis_result.get("rating")
            # 只有在评级为最高两档时才进行深度分析和邮件提醒
            if rating in ["特别牛逼", "牛逼"]:
                print("评级很高，开始进行详细分析...")
                detailed_analysis = await extract_details_with_ai(aggregated_summary)
                subject = f"Telegram监控提醒: 发现 {rating} 的内容！"
                body = (
                    f"AI分析系统在监控的Telegram群组中发现了重要内容（基于过去30分钟的消息）。\n\n"
                    f"评级: {rating}\n"
                    f"原因: {analysis_result.get('reason')}\n\n"
                    f"{detailed_analysis}\n\n"
                    f"原始数据已存入日志文件: {filename}"
                )
                await send_email_alert(subject, body)
    # --------------------------

    # 清空消息字典，为下一次收集做准备
    for group_id in target_groups:
        messages_dict[group_id] = []

    print("\n消息已更新，等待下一次收集...\n")


# 设置消息监听器
@client.on(events.NewMessage)
async def handle_new_message(event):
    # 检查消息是否来自目标群组
    if event.chat_id in target_groups:
        try:
            sender = await event.get_sender()
            sender_name = "未知来源"
            # 优先使用 event.sender_id 作为备用
            sender_id = event.sender_id or 0

            if sender:
                sender_id = sender.id
                # 安全地组合 first_name 和 last_name，过滤掉 None 或空字符串
                name_parts = filter(None, [sender.first_name, sender.last_name])
                full_name = " ".join(name_parts)
                sender_name = full_name.strip() or f"用户{sender_id}"

            # 获取消息文本
            message_text = event.message.text or "（非文本消息）"

            # 存储消息
            messages_dict[event.chat_id].append(
                {"sender_id": sender_id, "sender": sender_name, "text": message_text, "time": event.date.isoformat()}
            )
        except Exception as e:
            print(f"处理新消息时出错: {e}")


async def run_ai_test():
    """运行一次对最新日志文件的AI分析测试。"""
    log_dir = "telegram_logs"
    if not os.path.isdir(log_dir):
        print(f"错误: 日志目录 '{log_dir}' 不存在。")
        return

    json_files = [f for f in os.listdir(log_dir) if f.endswith(".json")]
    if not json_files:
        print(f"错误: 在 '{log_dir}' 中没有找到任何日志文件。")
        return

    latest_log_file = max(json_files)
    log_path = os.path.join(log_dir, latest_log_file)
    print(f"正在使用最新的日志文件进行分析: {log_path}")

    try:
        with open(log_path, "r", encoding="utf-8") as f:
            summary_data = json.load(f)
    except (json.JSONDecodeError, IOError) as e:
        print(f"错误: 无法读取或解析日志文件 {log_path}: {e}")
        return

    # 调用核心分析函数
    await analyze_messages_with_ai(summary_data)


async def health_check(request):
    """健康检查端点处理函数。"""
    global is_ai_healthy
    if is_ai_healthy:
        return web.json_response({"status": "ok"})
    else:
        # 使用 503 Service Unavailable 状态码表示服务暂时不可用
        return web.json_response(
            {"status": "unhealthy", "reason": "AI service is currently failing"},
            status=503,
        )


async def cleanup_old_logs():
    """清理指定天数之前的旧日志文件（在单独的线程中以避免阻塞）。"""

    def _do_cleanup():
        log_dir = "telegram_logs"
        days_to_keep = 3
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        if not os.path.isdir(log_dir):
            return  # 日志目录不存在，无需操作

        print(f"\n===== 开始日志清理 (删除{days_to_keep}天前的日志) =====")
        for filename in os.listdir(log_dir):
            file_path = os.path.join(log_dir, filename)
            try:
                if os.path.isfile(file_path):
                    file_mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_mod_time < cutoff_date:
                        os.remove(file_path)
                        print(f"已删除旧日志文件: {filename}")
            except Exception as e:
                print(f"删除文件 {file_path} 时出错: {e}")
        print("===== 日志清理完成 =====")

    await asyncio.to_thread(_do_cleanup)


async def main():
    global target_groups, messages_dict
    runner = None  # 初始化runner

    # --- 仅在监控模式下加载和验证目标群组 ---
    target_groups_str = os.getenv("TARGET_GROUPS")
    if not target_groups_str:
        print("错误: 未在 .env 文件中找到或设置 TARGET_GROUPS。")
        print("请先使用 'python main.py list-chats' 命令获取ID并配置.env文件。")
        return
    target_groups = [int(group_id.strip()) for group_id in target_groups_str.split(",")]
    messages_dict = {group_id: [] for group_id in target_groups}
    # ---

    # 连接并登录到Telegram
    await client.connect()
    if not await ensure_login():
        await client.disconnect()
        return

    # --- 新增: 程序启动时执行一次日志清理 ---
    await cleanup_old_logs()
    # ------------------------------------

    # --- 新增：启动健康检查Web服务 ---
    app = web.Application()
    app.router.add_get("/health", health_check)
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, "0.0.0.0", 63421)
    await site.start()
    print("\n健康检查接口已在 http://0.0.0.0:63421/health 启动")
    # --------------------------------

    # 设置定时任务
    scheduler = AsyncIOScheduler()
    scheduler.add_job(process_collected_messages, "interval", minutes=15)
    scheduler.add_job(cleanup_old_logs, "interval", days=1)  # <-- 新增此行
    scheduler.start()

    print("监控已启动，每15分钟将更新一次消息...")
    print("按 CTRL+C 可安全退出。")

    try:
        # 保持程序运行
        await client.run_until_disconnected()
    finally:
        # 确保所有资源被正确关闭
        print("\n正在关闭定时任务...")
        scheduler.shutdown()

        # --- 新增：关闭Web服务 ---
        if runner:
            await runner.cleanup()
            print("健康检查接口已关闭。")
        # -------------------------
        # 在退出前处理最后一批收集到的消息
        if any(messages_dict.values()):
            print("正在保存最后一批消息...")
            await process_collected_messages()
        if client.is_connected():
            print("正在断开Telegram客户端连接...")
            await client.disconnect()
        print("程序已安全退出。")


# 运行主函数
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Telegram Monitor: 监控、列出或测试服务的工具。")
    parser.add_argument(
        "command",
        nargs="?",
        default="monitor",
        choices=["monitor", "list-chats", "test-email", "test-ai"],
        help="要执行的命令: 'monitor' (默认) 启动监控, 'list-chats' 列出所有聊天, 'test-email' 发送测试邮件, 'test-ai' 对最新日志运行AI分析。",
    )
    args = parser.parse_args()

    if args.command == "list-chats":
        print("模式: 列出所有聊天和频道ID")
        asyncio.run(list_chats())
    elif args.command == "test-email":
        print("模式: 发送测试邮件")
        subject = "Telegram监控 - 测试邮件"
        body = f"这是一封测试邮件，发送于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}。\n如果收到这封邮件，说明您的邮件配置正确。"
        send_email_alert(subject, body)
    elif args.command == "test-ai":
        print("模式: 对最新的日志文件运行AI分析测试")
        asyncio.run(run_ai_test())
    else:  # args.command == 'monitor'
        print("模式: 启动消息监控")
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            # 捕获最终的Ctrl+C信号，安静地退出，不显示traceback
            pass
