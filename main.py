import os
from decimal import Decimal, InvalidOperation
import asyncio
import math

import boto3
from botocore.exceptions import ClientError
import gate_api
import requests
from dotenv import load_dotenv
from gate_api.exceptions import ApiException, GateApiException
from aiohttp import web

# 从配置文件导入代币列表
from config import XSTOCKS_CONTRACTS

# 立即加载环境变量，确保全局变量能正确初始化
load_dotenv()

# 全局价差阈值 (5%)
SPREAD_THRESHOLD = Decimal("5.0")

# 全局健康状态，用于健康检查接口
is_healthy = True


def get_solana_prices(token_addresses):
    """使用 Jupiter Price API V3 批量获取 Solana 代币价格，并处理分页。"""
    all_prices = {}
    num_addresses = len(token_addresses)
    if not num_addresses:
        return all_prices

    # Jupiter API V3 限制每次最多查询50个ID。
    # 新逻辑：计算最少需要的批次数，然后将总数除以批次数来得到每批的大小，
    # 这样可以使每批的大小尽可能均匀。
    JUPITER_API_CHUNK_LIMIT = 50
    num_batches = math.ceil(num_addresses / JUPITER_API_CHUNK_LIMIT)

    # 根据计算出的批次数，确定每批的实际大小
    chunk_size = math.ceil(num_addresses / num_batches)

    # 将地址列表分块
    for i in range(0, num_addresses, chunk_size):
        chunk = token_addresses[i : i + chunk_size]
        ids = ",".join(chunk)
        url = (
            f"https://lite-api.jup.ag/price/v3?ids={ids}"  # 修正：将 v3 移到 price 后面
        )

        try:
            print(
                f"正在查询 {len(chunk)} 个 Solana 代币的价格 (批次 {i // chunk_size + 1})..."
            )
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()  # V3 API 直接返回数据，没有 'data' 键

            # 从当前批次的响应中提取价格
            chunk_prices = {
                addr: item["usdPrice"]
                for addr, item in data.items()
                # 新增条件：确保 priceChange24h 存在，以过滤不活跃代币
                if item and "usdPrice" in item and "priceChange24h" in item
            }
            all_prices.update(chunk_prices)

        except requests.exceptions.RequestException as e:
            print(f"获取 Solana 价格批次时出错: {e}")
            # 即使一个批次失败，也继续尝试下一个
            continue

    return all_prices


def get_gate_price(spot_api, currency_pair):
    """使用 SpotApi 获取指定交易对的最新成交价。"""
    try:
        api_response = spot_api.list_tickers(currency_pair=currency_pair)
        if api_response:
            return api_response[0].last
    except GateApiException as ex:
        # 修正：检查 ex.label 而不是 ex.message，因为 message 可能为 None
        if ex.label != "CURRENCY_PAIR_NOT_FOUND":
            print(
                f"Gate API exception for {currency_pair}, label: {ex.label}, message: {ex.message}"
            )
    except ApiException as e:
        print(f"查询 {currency_pair} 时发生异常: {e}\n")
    return None


def get_futures_price(futures_api, contract_name):
    """使用 FuturesApi 获取指定永续合约的最新成交价。"""
    try:
        # Gate.io USDT 永续合约的 settle 参数固定为 'usdt'
        api_response = futures_api.list_futures_tickers(
            settle="usdt", contract=contract_name
        )
        if api_response:
            return api_response[0].last
    except GateApiException as ex:
        # 修正：检查 ex.label 而不是 ex.message，因为 message 可能为 None
        if ex.label != "CONTRACT_NOT_FOUND":
            print(
                f"Gate API exception for futures {contract_name}, label: {ex.label}, message: {ex.message}"
            )
    except ApiException as e:
        print(f"查询合约 {contract_name} 时发生异常: {e}\n")
    return None


# 加载邮件配置
RECIPIENT_EMAIL = os.getenv("RECIPIENT_EMAIL")
SENDER_EMAIL = os.getenv("SENDER_EMAIL")
AWS_REGION = os.getenv("AWS_REGION")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")


async def send_email_alert(subject, body):
    """使用 AWS SES 发送邮件提醒（在单独的线程中以避免阻塞）。"""

    def _send(subject_inner, body_inner):
        """包含所有阻塞代码的内部同步函数。"""
        if not all(
            [
                RECIPIENT_EMAIL,
                SENDER_EMAIL,
                AWS_REGION,
                AWS_ACCESS_KEY_ID,
                AWS_SECRET_ACCESS_KEY,
            ]
        ):
            print("警告: AWS SES 邮件配置不完整，跳过发送。请检查.env文件。")
            return

        # 为邮件正文创建HTML版本
        html_body = f"""
        <html>
        <head></head>
        <body>
          <p>{body_inner.replace("\n", "<br>")}</p>
        </body>
        </html>
        """
        CHARSET = "UTF-8"

        # 创建 SES 客户端
        ses_client = boto3.client(
            "ses",
            region_name=AWS_REGION,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        )

        print(f"正在通过 AWS SES 向 {RECIPIENT_EMAIL} 发送邮件提醒...")
        try:
            response = ses_client.send_email(
                Destination={"ToAddresses": [RECIPIENT_EMAIL]},
                Message={
                    "Body": {
                        "Html": {"Charset": CHARSET, "Data": html_body},
                        "Text": {"Charset": CHARSET, "Data": body_inner},
                    },
                    "Subject": {"Charset": CHARSET, "Data": subject_inner},
                },
                Source=SENDER_EMAIL,
            )
            print(f"--- 邮件发送成功! Message ID: {response['MessageId']} ---")
        except ClientError as e:
            error_message = e.response.get("Error", {}).get("Message", "未知错误")
            print(f"--- 邮件发送失败: {error_message} ---")
        except Exception as e:
            print(f"--- 邮件发送时发生未知异常: {e} ---")

    await asyncio.to_thread(_send, subject, body)


async def health_check(request):
    """健康检查端点处理函数。"""
    global is_healthy
    if is_healthy:
        return web.json_response({"status": "ok"})
    else:
        # 使用 503 Service Unavailable 状态码表示服务暂时不可用
        return web.json_response(
            {"status": "unhealthy", "reason": "A critical service is failing"},
            status=503,
        )


async def check_prices_and_alert(last_alerted_spreads):
    """执行单次价格检查和警报的核心逻辑，包含现货、合约和链上价格。"""
    global is_healthy
    is_healthy = True  # 每次检查开始时，假设是健康的

    # --- API 和服务设置 ---
    gate_api_key = os.getenv("GATE_API_KEY")
    gate_api_secret = os.getenv("GATE_API_SECRET")

    if not (gate_api_key and gate_api_secret):
        print("错误：Gate.io API 密钥或密钥未设置。请检查您的 .env 文件。")
        is_healthy = False  # <--- 在这里添加
        return

    configuration = gate_api.Configuration(key=gate_api_key, secret=gate_api_secret)
    api_client = gate_api.ApiClient(configuration)
    spot_api = gate_api.SpotApi(api_client)
    futures_api = gate_api.FuturesApi(api_client)  # 新增：初始化 Futures API

    print(f"已加载 {len(XSTOCKS_CONTRACTS)} 个 xStocks 合约地址。")
    print("-" * 80)

    # 1. 批量获取所有 Solana 代币的价格
    solana_addresses = [contract["contractAddress"] for contract in XSTOCKS_CONTRACTS]
    solana_prices = get_solana_prices(solana_addresses)

    if not solana_prices:
        print("无法获取任何 Solana 价格，本轮检查中止。")
        is_healthy = False  # <--- 在这里添加
        return

    # 2. 遍历代币，获取所有价格并进行比较
    for contract_info in XSTOCKS_CONTRACTS:
        ticker = contract_info["ticker"]
        sol_address = contract_info["contractAddress"]
        full_name = contract_info["fullName"]
        base_currency = ticker.replace(".", "")
        # Gate.io 交易对和合约名称
        gate_pair_name = f"{base_currency.upper()}_USDT"

        # 获取所有价格源
        spot_price_str = get_gate_price(spot_api, gate_pair_name)
        futures_price_str = get_futures_price(futures_api, gate_pair_name)
        sol_price_float = solana_prices.get(sol_address)

        # 将获取到的有效价格(>=1)存入字典
        prices = {}
        try:
            if spot_price_str:
                price = Decimal(spot_price_str)
                if price >= 1:
                    prices["spot"] = price
            if futures_price_str:
                price = Decimal(futures_price_str)
                if price >= 1:
                    prices["futures"] = price
            if sol_price_float:
                price = Decimal(str(sol_price_float))
                if price >= 1:
                    prices["solana"] = price
        except InvalidOperation:
            print(f"{ticker:<8s} | 价格数据格式无效，跳过。")
            continue

        # 格式化用于打印的价格字符串
        spot_p_str = f"${prices['spot']}" if "spot" in prices else "N/A"
        futures_p_str = f"${prices['futures']}" if "futures" in prices else "N/A"
        solana_p_str = f"${prices['solana']:.8f}" if "solana" in prices else "N/A"
        print(
            f"{ticker:<8s} | Spot: {spot_p_str:<12} | Futures: {futures_p_str:<12} | Solana: {solana_p_str:<15}"
        )

        # 检查是否有至少两种价格可供比较
        if len(prices) < 2:
            continue

        # 定义所有需要检查的价差组合
        spread_combinations = [
            ("spot", "solana"),
            ("futures", "solana"),
            ("spot", "futures"),
        ]

        alert_triggered_for_ticker = False
        for p1_key, p2_key in spread_combinations:
            if p1_key in prices and p2_key in prices:
                p1 = prices[p1_key]
                p2 = prices[p2_key]

                if p2 == 0:
                    continue  # 避免除以零

                spread = ((p1 - p2) / p2) * 100
                abs_spread = abs(spread)

                # 为每个价差组合生成唯一的键
                alert_key = (ticker, p1_key, p2_key)
                last_spread = last_alerted_spreads.get(alert_key)

                # 1. 如果价差回落到阈值以下，重置提醒状态
                if abs_spread <= SPREAD_THRESHOLD:
                    if alert_key in last_alerted_spreads:
                        del last_alerted_spreads[alert_key]
                        print(
                            f"价差回落: {ticker} ({p1_key}/{p2_key}) 的价差 {spread:+.2f}% 已低于阈值，重置提醒。"
                        )
                    continue

                # 2. 检查是否需要发送新提醒
                should_alert = False
                if last_spread is None:
                    # 之前未提醒过，首次触发
                    should_alert = True
                else:
                    # 之前提醒过，检查价差是否跨越了新的 SPREAD_THRESHOLD 整数倍
                    last_alert_tier = last_spread // SPREAD_THRESHOLD
                    current_tier = abs_spread // SPREAD_THRESHOLD
                    if current_tier > last_alert_tier:
                        should_alert = True

                if should_alert:
                    subject = (
                        f"价格警报: {ticker} ({full_name}) 发现显著价差 {spread:+.2f}%"
                    )
                    body = (
                        f"代币 {ticker} ({full_name}) 出现显著价格差异。\n"
                        f"合约地址: {sol_address}\n\n"
                        f"监控到的价格如下:\n"
                        f"  - Gate.io 现货: {spot_p_str}\n"
                        f"  - Gate.io 合约: {futures_p_str}\n"
                        f"  - Solana 链上: {solana_p_str}\n\n"
                        f"触发警报的价差:\n"
                        f"  - {p1_key.capitalize()} vs {p2_key.capitalize()}: {spread:+.2f}%\n\n"
                        "请核实并采取相应行动。"
                    )
                    await send_email_alert(subject, body)

                    # 更新或记录本次提醒的价差
                    last_alerted_spreads[alert_key] = abs_spread
                    alert_triggered_for_ticker = True
                    break  # 每个代币每轮只发送一次邮件
                else:
                    # 价差仍高于阈值，但未达到再次提醒的条件
                    next_alert_tier_value = (
                        last_spread // SPREAD_THRESHOLD + Decimal("1")
                    ) * SPREAD_THRESHOLD
                    print(
                        f"价差已提醒: {ticker} ({p1_key}/{p2_key}) 当前价差 {spread:+.2f}% 未达到下一提醒层级 (>{next_alert_tier_value:.2f}%)。"
                    )

        if alert_triggered_for_ticker:
            print("-" * 80)

    print("-" * 80)
    print("监控完成。")


async def main():
    """主循环，每5分钟运行一次价格检查，并提供健康检查接口。"""
    # --- 启动健康检查Web服务 ---
    app = web.Application()
    app.router.add_get("/health", health_check)
    runner = web.AppRunner(app)
    await runner.setup()
    # 监听端口可以根据需要修改
    site = web.TCPSite(runner, "0.0.0.0", 63425)
    await site.start()
    print("\n健康检查接口已在 http://0.0.0.0:63425/health 启动")
    # --------------------------------

    last_alerted_spreads = {}  # 在此处初始化内存记录
    while True:
        print("\n--- 开始新一轮价格检查 ---")
        await check_prices_and_alert(last_alerted_spreads)  # 将状态传入
        print("--- 检查完成，等待5分钟... (按 Ctrl+C 退出) ---")
        await asyncio.sleep(300)  # 300秒 = 5分钟


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断。正在退出...")
