"""
AI工具模块
提供AI API调用的统一接口
"""

import json
import threading
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, overload

import numpy as np
from openai import APIStatusError, OpenAI
from pydantic import BaseModel

from config.prompts import (
    AUDIO_ANALYSIS_PROMPT,
    SCENE_ANALYSIS_PROMPT_ACTION,
    SCENE_ANALYSIS_PROMPT_COMEDY,
    SCENE_ANALYSIS_PROMPT_DEFAULT,
    SCENE_ANALYSIS_PROMPT_DRAMA,
)
from config.settings import settings
from utils.logger import get_logger
from utils.shared_state import shutdown_event

logger = get_logger(__name__)

# 在 AIClient 类定义之前，添加一个类型变量
T = TypeVar("T", bound=BaseModel)


class BaseAPIClient:
    """
    通用的API客户端基类，封装了密钥轮换和重试逻辑。
    """

    def __init__(
        self,
        api_keys: List[str],
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        timeout: Optional[int] = None,
    ):
        self.api_keys = api_keys
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = settings.MAX_RETRIES
        self.retry_delay = settings.RETRY_DELAY

        if not self.api_keys:
            raise ValueError("API keys 未在配置中提供。")

        self._key_index = 0
        self._key_lock = threading.Lock()
        self._cooldown_keys: Dict[str, float] = {}  # key -> cooldown_end_timestamp

    def _get_next_api_key(self) -> str:
        """
        线程安全地获取下一个可用的API密钥，会跳过处于冷却状态的密钥。
        """
        with self._key_lock:
            # 遍历所有密钥最多一圈，以找到一个可用的
            for _ in range(len(self.api_keys)):
                key = self.api_keys[self._key_index]
                self._key_index = (self._key_index + 1) % len(self.api_keys)

                cooldown_end_time = self._cooldown_keys.get(key)
                if cooldown_end_time:
                    if time.time() < cooldown_end_time:
                        # 仍在冷却中，跳过
                        continue
                    else:
                        # 冷却已结束，从字典中移除
                        logger.info(f"API密钥 '...{key[-4:]}' 已结束冷却，恢复使用。")
                        del self._cooldown_keys[key]

                # 找到可用密钥，返回
                return key

            # 如果循环结束仍未找到可用密钥
            raise RuntimeError("所有API密钥当前都处于冷却状态，请稍后再试。")

    def _set_key_on_cooldown(self, api_key: str):
        """将指定的API密钥置于冷却状态"""
        with self._key_lock:
            cooldown_end_time = time.time() + settings.API_KEY_COOLDOWN_SECONDS
            self._cooldown_keys[api_key] = cooldown_end_time
            end_time_str = datetime.fromtimestamp(cooldown_end_time).strftime("%H:%M:%S")
            logger.warning(f"API密钥 '...{api_key[-4:]}' 因超出限制被置于冷却状态，直到 {end_time_str}")

    def _execute_with_retry(self, api_call_function: Callable[[OpenAI], Any]) -> Any:
        """
        执行API调用，并包含完整的重试和密钥冷却逻辑。
        :param api_call_function: 一个接收OpenAI客户端实例并执行API调用的函数。
        """
        last_exception = None
        for attempt in range(self.max_retries):
            api_key = self._get_next_api_key()  # 在每次尝试时都获取一个可能不同的、可用的key
            try:
                client = OpenAI(api_key=api_key, base_url=self.base_url, timeout=self.timeout)
                return api_call_function(client)
            except APIStatusError as e:
                last_exception = e
                # 专门处理API状态错误
                is_cooldown_error = False
                if e.status_code == 429:
                    try:
                        error_data = e.response.json()
                        if error_data.get("error", {}).get("code") == "SetLimitExceeded":
                            self._set_key_on_cooldown(api_key)
                            is_cooldown_error = True
                    except (json.JSONDecodeError, AttributeError):
                        pass  # 无法解析错误体，按通用错误处理

                logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries}, Key: '...{api_key[-4:]}'): {e}")

                if is_cooldown_error:
                    # 如果是冷却错误，不等待，立即用下一个可用key重试
                    if attempt < self.max_retries - 1:
                        continue
                    else:
                        logger.error("所有API密钥均达到限制，无法完成API调用。")
                        break  # 跳出循环，准备抛出异常

            except Exception as e:
                last_exception = e
                # 处理其他所有类型的异常
                logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries}, Key: '...{api_key[-4:]}'): {e}")

            # 对于非冷却错误或重试次数未用尽的情况，进行等待
            if attempt < self.max_retries - 1:
                if shutdown_event.wait(timeout=self.retry_delay):
                    logger.warning("检测到关闭信号，中断API重试。")
                    raise InterruptedError("API调用被用户中断")
            else:
                # 所有重试次数用尽
                logger.error(f"API调用最终失败: {last_exception}")
                break  # 确保跳出循环

        raise last_exception if last_exception is not None else RuntimeError("API调用意外结束，未返回结果也未抛出异常")


class AIClient(BaseAPIClient):
    """
    用于处理聊天和文本生成任务的AI客户端。
    """

    def __init__(
        self,
        api_keys: Optional[List[str]] = None,
        base_url: Optional[str] = None,
        model: Optional[str] = None,
        embedding_model: Optional[str] = None,
        timeout: Optional[int] = None,
    ):
        super().__init__(
            api_keys=api_keys if api_keys is not None else settings.VOLCENGINE_API_KEYS,
            base_url=base_url if base_url is not None else settings.VOLCENGINE_BASE_URL,
            model=model if model is not None else settings.VOLCENGINE_MODEL_NAME,
            timeout=timeout,
        )
        self.embedding_model = embedding_model

    def call_ai(
        self,
        prompt: Any,
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        extra_body: Optional[Dict[str, Any]] = None,
    ) -> str:
        """调用AI API，支持简单文本和多部分内容"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        if isinstance(prompt, str):
            messages.append({"role": "user", "content": prompt})
        elif isinstance(prompt, list):
            messages.append({"role": "user", "content": prompt})
        else:
            raise TypeError(f"不支持的prompt类型: {type(prompt)}")

        # 新增日志记录
        logger.debug(f"AI Request Payload:\n{json.dumps(messages, indent=2, ensure_ascii=False)}")

        def api_call(client: OpenAI) -> str:
            payload = {
                "model": self.model or settings.VOLCENGINE_MODEL_NAME,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "extra_body": extra_body,
            }
            payload = {k: v for k, v in payload.items() if v is not None}
            response = client.chat.completions.create(**payload)
            result = response.choices[0].message.content
            logger.debug(f"AI Raw Response Content:\n{result}")
            if not result:
                raise ValueError("AI返回了空内容，可能由于内容审查或模型内部错误。")
            return result

        return self._execute_with_retry(api_call)

    def call_ai_with_tool(
        self,
        prompt: Any,
        response_model: Type[T],
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
    ) -> T:
        """
        使用 client.beta.chat.completions.parse 强制AI返回符合Pydantic模型的对象。
        """
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        if isinstance(prompt, str):
            messages.append({"role": "user", "content": prompt})
        elif isinstance(prompt, list):
            messages.append({"role": "user", "content": prompt})
        else:
            raise TypeError(f"不支持的prompt类型: {type(prompt)}")

        # 新增日志记录
        logger.debug(f"AI Request (Tool Mode):\n{json.dumps(messages, indent=2, ensure_ascii=False)}")

        def api_call(client: OpenAI) -> T:
            completion = client.beta.chat.completions.parse(
                model=self.model or settings.VOLCENGINE_MODEL_NAME,
                messages=messages,
                response_format=response_model,
                temperature=temperature,
            )
            # 记录原始JSON输出以供调试
            if completion.choices and completion.choices[0].message and completion.choices[0].message.tool_calls:
                # 注意：实际的原始文本在 tool_calls 的 function.arguments 中
                raw_text = completion.choices[0].message.tool_calls[0].function.arguments
                logger.debug(f"AI Raw JSON Response:\n{raw_text}")

            parsed_obj = completion.choices[0].message.parsed
            if parsed_obj is None:
                raise ValueError("AI响应解析后为空，无法构建有效的Pydantic模型。")

            # --- 【核心修改】在此处添加debug日志 ---
            logger.debug(f"Parsed Pydantic Object:\n{parsed_obj.model_dump_json(indent=2, exclude_none=True)}")

            return parsed_obj

        return self._execute_with_retry(api_call)

    @overload
    def get_embedding(self, text: str) -> Optional[np.ndarray]: ...

    @overload
    def get_embedding(self, text: List[str]) -> Optional[List[np.ndarray]]: ...

    def get_embedding(self, text: Union[str, List[str]]) -> Optional[Union[np.ndarray, List[np.ndarray]]]:
        """
        为给定文本或文本列表生成嵌入向量。
        优先使用本地嵌入模型配置，如果未配置，则回退到使用高级分析AI的嵌入模型。
        """
        is_single_input = isinstance(text, str)
        input_texts = [text] if is_single_input else text

        if not input_texts:
            return [] if not is_single_input else None

        # 智能选择嵌入客户端和模型
        use_local_embedding = settings.EMBEDDING_API_BASE_URL and settings.EMBEDDING_MODEL_NAME

        if use_local_embedding:
            base_url, model_name = settings.EMBEDDING_API_BASE_URL, settings.EMBEDDING_MODEL_NAME
            api_key = settings.EMBEDDING_API_KEY or "local-key"
            logger.debug(f"正在使用本地嵌入模型: {model_name} @ {base_url}")
        else:
            advanced_config = settings.get_advanced_ai_config()
            base_url, model_name = advanced_config["base_url"], advanced_config.get("embedding_model")
            api_key = self._get_next_api_key()
            logger.debug(f"正在使用高级AI嵌入模型: {model_name}")

        if not model_name:
            logger.error("未配置任何有效的嵌入模型，无法生成向量。")
            return None

        try:
            embedding_client = OpenAI(api_key=api_key, base_url=base_url)
            response = embedding_client.embeddings.create(input=input_texts, model=model_name)
            embeddings = [np.array(e.embedding, dtype=np.float32) for e in response.data]

            if is_single_input:
                return embeddings[0] if embeddings else None
            else:
                return embeddings
        except Exception as e:
            logger.error(f"使用模型 {model_name} 生成文本嵌入时出错: {e}")
            return None

    def analyze_scene(self, video_url: str, duration: float, genre: str = "personal") -> Dict[str, Any]:
        from config.schemas import SceneAnalysisResponse

        prompt_map = {
            "action": SCENE_ANALYSIS_PROMPT_ACTION,
            "drama": SCENE_ANALYSIS_PROMPT_DRAMA,
            "comedy": SCENE_ANALYSIS_PROMPT_COMEDY,
            "personal": SCENE_ANALYSIS_PROMPT_DEFAULT,
        }
        prompt_template = prompt_map.get(genre, SCENE_ANALYSIS_PROMPT_DEFAULT)
        text_prompt = prompt_template.format(duration=duration)

        multi_part_content = [
            {
                "type": "video_url",
                "video_url": {"url": video_url, "fps": settings.SCENE_VIDEO_FPS, "detail": settings.SCENE_VIDEO_DETAIL},
            },
            {"type": "text", "text": text_prompt},
        ]
        logger.info(f"开始使用多模态模型分析视频片段: {video_url}")
        result = self.call_ai_with_tool(multi_part_content, response_model=SceneAnalysisResponse, temperature=0.2)
        return result.model_dump()

    def analyze_audio(self, audio_url: str) -> Dict[str, Any]:
        from config.schemas import AudioAnalysisResponse

        multi_part_content = [
            {"type": "audio_url", "audio_url": {"url": audio_url}},
            {"type": "text", "text": AUDIO_ANALYSIS_PROMPT},
        ]
        logger.info(f"开始分析音频片段: {audio_url}")
        result = self.call_ai_with_tool(multi_part_content, response_model=AudioAnalysisResponse, temperature=0.2)
        return result.model_dump()


ai_client = AIClient()
