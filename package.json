{"name": "@browserbasehq/stagehand", "version": "2.4.1", "description": "An AI web browsing framework focused on simplicity and extensibility.", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"example": "pnpm --filter @browserbasehq/stagehand-examples run start", "format": "prettier --write .", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "eslint": "eslint .", "cache:clear": "rm -rf .cache", "docs": "pnpm --filter @browserbasehq/stagehand-docs run dev", "evals": "tsx scripts/run-evals.ts", "evals:help": "pnpm run evals -- help", "evals:man": "pnpm run evals -- man", "e2e": "pnpm run build && cd evals/deterministic && playwright test --config=e2e.playwright.config.ts", "e2e:bb": "pnpm run build && cd evals/deterministic && playwright test --config=bb.playwright.config.ts", "e2e:local": "pnpm run build && cd evals/deterministic && playwright test --config=local.playwright.config.ts", "build-dom-scripts": "tsx lib/dom/genDomScripts.ts", "build-types": "tsc --emitDeclarationOnly --outDir dist", "build-js": "tsup lib/index.ts --dts", "build": "pnpm run lint && pnpm run gen-version && pnpm run build-dom-scripts && pnpm run build-js && pnpm run build-types", "gen-version": "tsx scripts/gen-version.ts", "prepare": "pnpm run build", "lint": "pnpm run prettier:fix && pnpm run eslint", "release": "pnpm run build && changeset publish", "release-canary": "pnpm run build && changeset version --snapshot && changeset publish --tag alpha"}, "files": ["dist/**"], "keywords": [], "author": "Browserbase", "license": "MIT", "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.9", "@eslint/js": "^9.16.0", "@langchain/core": "^0.3.40", "@langchain/openai": "^0.4.4", "@types/adm-zip": "^0.5.7", "@types/cheerio": "^0.22.35", "@types/express": "^4.17.21", "@types/node": "^20.11.30", "@types/ws": "^8.5.13", "adm-zip": "^0.5.16", "autoevals": "^0.0.64", "braintrust": "^0.0.171", "chalk": "^5.4.1", "cheerio": "^1.0.0", "chromium-bidi": "^0.10.0", "esbuild": "^0.21.4", "eslint": "^9.16.0", "express": "^4.21.0", "globals": "^15.13.0", "multer": "^1.4.5-lts.1", "@playwright/test": "^1.42.1", "prettier": "^3.2.5", "string-comparison": "^1.3.0", "tsup": "^8.2.1", "tsx": "^4.10.5", "typescript": "^5.2.2", "typescript-eslint": "^8.17.0"}, "peerDependencies": {"deepmerge": "^4.3.1", "dotenv": "^16.4.5", "zod": "^3.23.8"}, "dependencies": {"@anthropic-ai/sdk": "0.39.0", "@browserbasehq/sdk": "^2.4.0", "@google/genai": "^0.8.0", "ai": "^4.3.9", "devtools-protocol": "^0.0.1464554", "fetch-cookie": "^3.1.0", "openai": "^4.87.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "playwright": "^1.52.0", "ws": "^8.18.0", "zod-to-json-schema": "^3.23.5"}, "optionalDependencies": {"@ai-sdk/anthropic": "^1.2.6", "@ai-sdk/azure": "^1.3.19", "@ai-sdk/cerebras": "^0.2.6", "@ai-sdk/deepseek": "^0.2.13", "@ai-sdk/google": "^1.2.6", "@ai-sdk/groq": "^1.2.4", "@ai-sdk/mistral": "^1.2.7", "@ai-sdk/openai": "^1.0.14", "@ai-sdk/perplexity": "^1.1.7", "@ai-sdk/togetherai": "^0.2.6", "@ai-sdk/xai": "^1.2.15", "ollama-ai-provider": "^1.2.0"}, "directories": {"doc": "docs", "example": "examples", "lib": "lib"}, "repository": {"type": "git", "url": "git+https://github.com/browserbase/stagehand.git"}, "bugs": {"url": "https://github.com/browserbase/stagehand/issues"}, "homepage": "https://stagehand.dev", "overrides": {"whatwg-url": "^14.0.0"}, "packageManager": "pnpm@9.15.0+sha512.76e2379760a4328ec4415815bcd6628dee727af3779aaa4c914e3944156c4299921a89f976381ee107d41f12cfa4b66681ca9c718f0668fa0831ed4c6d8ba56c"}