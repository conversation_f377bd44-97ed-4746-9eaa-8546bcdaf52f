# AI驱动的自动化视频剪辑项目配置文件

# ===== 数据库配置 (PostgreSQL) - 必需 =====
# 数据库主机
POSTGRES_HOST=localhost
# 数据库端口
POSTGRES_PORT=5432
# 数据库名称
POSTGRES_DB=autocutter
# 数据库用户名
POSTGRES_USER=your_db_user
# 数据库密码
POSTGRES_PASSWORD=your_db_password

# ===== AI API配置 - 必需 =====
# VOLCENGINE 支持配置多个API Key，用英文逗号分隔，程序会自动轮换使用
VOLCENGINE_API_KEY=your_key_1,your_key_2,your_key_3

# VOLCENGINE API基础URL（可选，默认为官方API）
VOLCENGINE_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# VOLCENGINE 使用的模型名称（可选，默认为doubao-seed-1-6-250615）
VOLCENGINE_MODEL_NAME=doubao-seed-1-6-250615

# GROK 支持配置多个API Key，用英文逗号分隔，程序会自动轮换使用
GROK_API_KEY=your_key_1,your_key_2,your_key_3

# GROK API基础URL（可选，默认为官方API）
GROK_BASE_URL=https://api.ephone.chat/v1

# GROK 使用的模型名称（可选，默认为grok-3-deepsearch）
GROK_MODEL_NAME=grok-3-deepsearch

# GROK 超时时间（秒，默认600）
GROK_TIMEOUT=600

# ===== 高级分析AI配置 (用于场景聚合、序列分析、剧本创作) - 可选 =====
# ADVANCED_AI 支持配置多个API Key，用英文逗号分隔，程序会自动轮换使用
ADVANCED_AI_API_KEY=your_advanced_key_1,your_advanced_key_2

# ADVANCED_AI API基础URL（可配置为任何与OpenAI兼容的端点）
ADVANCED_AI_BASE_URL=https://api.openai.com/v1

# ADVANCED_AI 使用的模型名称
ADVANCED_AI_MODEL_NAME=gpt-4o

# ADVANCED_AI 使用的嵌入模型名称 (例如: text-embedding-3-small)
ADVANCED_AI_EMBEDDING_MODEL=text-embedding-3-small

# ADVANCED_AI 超时时间（秒，默认600）
ADVANCED_AI_TIMEOUT=600

# ===== 本地嵌入模型配置 (可选, 用于向量化) =====
# 本地嵌入模型的API基础URL (例如: http://localhost:11434/v1)
EMBEDDING_API_BASE_URL=http://localhost:11434/v1
# 本地嵌入模型的名称 (例如: dengcao/Qwen3-Embedding-4B:Q5_K_M)
EMBEDDING_MODEL_NAME=dengcao/Qwen3-Embedding-4B:Q5_K_M
# 本地嵌入模型的API Key (如果需要的话，通常本地服务不需要，留空即可)
EMBEDDING_API_KEY=

# 每次调用嵌入模型时处理的文本数量 (默认: 5)
EMBEDDING_BATCH_SIZE=5

# ===== 视频处理配置 =====

# 镜头(Shot)检测阈值（默认30.0），值越高，镜头分割越少。
SCENE_DETECTION_THRESHOLD=30.0

# 场景检测器类型 ('adaptive', 'content', 'threshold')
SCENE_DETECTOR=adaptive

# scenedetect CLI的编码预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
SCENEDETECT_PRESET=ultrafast

# 新增：用于AI分析的低质量视频比特率 (例如: "500k")
LOW_QUALITY_BITRATE=500k

# 新增：提取的音频比特率 (例如: "96k")
AUDIO_BITRATE=96k

# 新增：AI分析视频帧率 (例如: 5, 每秒提取5帧进行分析)
SCENE_VIDEO_FPS=5

# 新增：AI分析视频细节级别 ("low" 或 "high")
SCENE_VIDEO_DETAIL=high

# 新增：FaceNet 使用的预训练模型 ('vggface2' 或 'casia-webface')
FACENET_PRETRAINED_MODEL=vggface2
# 新增：被认为是有效人脸的最小像素尺寸 (宽度或高度)
MIN_FACE_SIZE=40
# 新增：FaceNet 人脸检测的置信度阈值 (0.0 - 1.0, 越高越严格, 推荐0.9)
FACENET_CONFIDENCE_THRESHOLD=0.9
# 新增：人脸检测的视频帧采样率 (例如: 5, 每秒提取5帧进行人脸检测)
FACE_DETECTION_FRAME_SAMPLING_RATE=5

# 新增：【调试模式】如果设为 "true"，将在阶段1的人脸提取过程中，在 output/debug/faces 目录下保存中间图像以供诊断
FACE_DEBUG_MODE=false

# 新增：使用外部图片（如演员头像）进行角色标注时的余弦距离阈值 (推荐 0.4)
CHARACTER_MATCHING_THRESHOLD=0.4
# 新增：角色自动标注的建议阈值 (推荐 0.55, 应大于硬匹配阈值)
CHARACTER_SUGGESTION_THRESHOLD=0.55

# 新增：人脸提取时的图像模糊检测阈值 (拉普拉斯方差，值越高越严格)
BLUR_DETECTION_THRESHOLD=100.0

# 新增：DBSCAN角色聚类配置
DBSCAN_EPS=0.3
DBSCAN_MIN_SAMPLES=3

# 新增：在交互式命名阶段，为每个角色显示的代表性人脸图片数量
NAMING_FACE_COUNT=3

# ===== 搜索引擎配置 =====
# Tavily搜索API密钥（用于外部资料增强）
TAVILY_API_KEY=

# ===== TOS配置 =====
# TOS对象存储配置
TOS_ENDPOINT=https://tos-s3.example.com
TOS_ACCESS_KEY=your_access_key
TOS_SECRET_KEY=your_secret_key
TOS_REGION=us-east-1
TOS_BUCKET=your-bucket-name

# ===== TTS配置 =====
# 默认的TTS发音人 (可选: xiaoxiao, yunxi)，默认为 xiaoxiao
TTS_VOICE=xiaoxiao

# ===== 性能配置 =====
# 最大并发任务数（用于并行处理视频片段等，默认5）
MAX_CONCURRENT_REQUESTS=5

# 最大重试次数（默认3）
MAX_RETRIES=3

# 重试延迟（秒，默认1）
RETRY_DELAY=1

# ===== 日志配置 =====
# 日志级别 (DEBUG, INFO, WARNING, ERROR)，默认为 INFO
LOG_LEVEL=INFO

# ===== 新增：调试模式 =====
# 如果设置为大于0的整数（例如 1），Stage16和Stage18将只处理前 N 个场景，用于快速调试。
# 设为 0 或注释掉此行，则处理所有场景。
DEBUG_MODE_SCENE_LIMIT=0

# ===== API密钥冷却配置 =====
# 当API密钥因达到速率限制而被临时禁用时，需要冷却的秒数 (默认 300 秒)
API_KEY_COOLDOWN_SECONDS=300

# ===== 转写配置 (mlx-whisper) =====
# 使用的 Whisper 模型 (来自 huggingface.co/mlx-community)
WHISPER_MODEL=mlx-community/whisper-large-v3-turbo
# 转写的目标语言 (例如: en, zh, ru, ja)
WHISPER_LANGUAGE=en

# ===== VAD 配置 (silero-vad) =====
# VAD 模型检测语音的置信度阈值 (0.0 - 1.0, 推荐 0.5)
VAD_THRESHOLD=0.5
# 被认为是静音的最小持续时间（毫秒）
VAD_MIN_SILENCE_DURATION_MS=100
# 被认为是语音的最小持续时间（毫秒）
VAD_MIN_SPEECH_DURATION_MS=250

# --- 旁白时长估算 ---
# 平均每秒朗读多少个中文字符（或英文单词的字符数近似）
NARRATION_CHAR_PER_SEC=6.0
# 新增：旁白语言 (例如: 中文, English)
NARRATION_LANGUAGE=中文

 # 相邻句子对应的视频片段之间，允许的最大重叠秒数，以创建J-Cut/L-Cut效果
 MAX_OVERLAP_SECONDS=0.5

 # 新增：旁白出现时，原片音量降低的分贝数 (负值表示降低, -18是较好的选择)
 AUDIO_DUCKING_DB=-18

# ===== 剪辑策略配置 (Stage 16) =====
# 为一句旁白匹配的最大镜头数量
MAX_SHOTS_PER_SENTENCE=5
# 确保选中的镜头总时长至少为 `旁白时长 * 此系数`，以提供足够的视觉素材
SHOTS_TOTAL_DURATION_FACTOR=1.1
# 在将候选镜头送给AI进行精选之前，通过语义搜索最多保留多少个最相关的镜头
MAX_CANDIDATE_SHOTS_FOR_AI=15

