# 从 my.telegram.org 获取
API_ID=
API_HASH=

# 你的Telegram电话号码，包含国家代码
PHONE_NUMBER=

# 会话文件名 (可选, 默认为 telegram)
SESSION_NAME=telegram

# 目标群组ID，用逗号分隔，例如: -100123456789,-100987654321
TARGET_GROUPS=

# 代理配置 (可选, 如果不需要请留空)
PROXY_TYPE=socks5
PROXY_HOST=127.0.0.1
PROXY_PORT=6785

# --- OpenAI/DeepSeek API 配置 ---
# 请使用您的 DeepSeek API 密钥
OPENAI_API_KEY=
# DeepSeek 的 API 端点
OPENAI_API_BASE=https://api.deepseek.com/v1
# 使用的模型名称
AI_MODEL_NAME=deepseek-chat

# --- 邮件提醒配置 (使用 AWS SES) ---
# 接收提醒的邮箱地址
RECIPIENT_EMAIL=
# 发送邮件的邮箱地址 (必须是在 AWS SES 中验证过的身份)
SENDER_EMAIL=
# 您的 AWS 区域 (例如: us-east-1)
AWS_REGION=
# 您的 AWS 访问密钥 ID
AWS_ACCESS_KEY_ID=
# 您的 AWS 私有访问密钥
AWS_SECRET_ACCESS_KEY=
